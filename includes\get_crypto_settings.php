<?php
session_start();
include "conn.php"; // Connessione al database

$data = json_decode(file_get_contents("php://input"), true);
$crypto = $data["crypto"];

// Prende i parametri della crypto selezionata per l’utente loggato
$sql = "SELECT s_bid, multiplicator, digit_num, slippage_num FROM crypto WHERE crypto_name = ? AND user = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("ss", $crypto, $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result()->fetch_assoc();

echo json_encode($result);
?>
