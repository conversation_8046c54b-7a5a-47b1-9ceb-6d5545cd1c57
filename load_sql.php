<?php
// load_sql.php – Carica un file SQL e ripristina il database crypto_bot

$host = "localhost";
$user = "root";
$password = ""; // aggiungi la password se serve
$dbname = "crypto_bot";

// Connessione al database
$conn = new mysqli($host, $user, $password, $dbname);
if ($conn->connect_error) {
    die("❌ Connessione fallita: " . $conn->connect_error);
}

// Controllo file caricato
if (!isset($_FILES['sqlfile']) || $_FILES['sqlfile']['error'] != UPLOAD_ERR_OK) {
    echo "❌ Nessun file SQL caricato.";
    exit;
}

// Leggo il contenuto del file SQL
$sqlContent = file_get_contents($_FILES['sqlfile']['tmp_name']);
if (!$sqlContent) {
    echo "❌ File SQL vuoto o non leggibile.";
    exit;
}

// Disabilito i controlli di foreign key
$conn->query("SET foreign_key_checks = 0");

// Divido in singole query
$queries = explode(";", $sqlContent);
foreach ($queries as $query) {
    $query = trim($query);
    if ($query) {
        $conn->query($query);
    }
}

$conn->query("SET foreign_key_checks = 1");
$conn->close();

echo "✅ Database crypto_bot ripristinato correttamente!";
?>
