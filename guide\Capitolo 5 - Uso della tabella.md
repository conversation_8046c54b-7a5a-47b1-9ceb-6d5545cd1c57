# 📗 Capitolo 5 – Us<PERSON> della tabella

---

## 🎯 Obiettivo del capitolo

In questo capitolo analizziamo nel dettaglio la **tabella degli ordini** che si trova nella parte bassa della pagina `trade.php`.

Scoprirai:

- Come leggere e interpretare ogni colonna
- Cosa significano i valori `FULL`, `open`, `empty`
- Quando e come si aggiorna la tabella
- Dove vengono salvati i dati
- Come verificare l’attività degli ordini

---

## 📊 Dove si trova la tabella?

La tabella si trova nella sezione:

```
<section id="data-table">
  <table class="table table-striped table-hover text-center">
```

Viene generata e aggiornata dinamicamente in base alla crypto selezionata e ai target price impostati.

---

## 📐 Struttura della tabella

### Esempio (visivo semplificato):

| ID        | Order State | Order Level | Target Price | % Price Dev | Price Dev | Actual Expense | Gains (50%) |
|-----------|-------------|-------------|--------------|--------------|-----------|----------------|--------------|
| level46   | FULL        | 1           | 0.00001131   | +0.98%       | 0.0000001 | 0.00016826     | 0.09190      |
| level45   | open        | 2           | 0.00001103   | -1.52%       | -0.0000002| 0.00018737     | 0.10598      |

---

## 🔍 Colonne spiegate

| Colonna                | Significato                                                                 |
|------------------------|------------------------------------------------------------------------------|
| **ID**                 | Nome del livello (es. `level45`), unico per ogni riga                       |
| **Order State**        | Stato dell’ordine: `open`, `full`, vuoto                                    |
| **Order Level**        | Numero relativo al livello (0 = centrale, negativi = più bassi)             |
| **Target Price**       | Prezzo target per quell’ordine                                               |
| **% Price Deviation**  | Scostamento percentuale rispetto al prezzo attuale                          |
| **Price Deviation**    | Scostamento assoluto (in valore) dal prezzo attuale                         |
| **Actual Expense**     | Importo calcolato per comprare/vendere a quel livello                       |
| **Gains (50%)**        | Ricavo stimato al 50% di guadagno (calcolato sul prezzo target successivo)  |

---

## 🔁 Stato ordini (Order State)

| Stato     | Significato                                      | Visuale |
|-----------|--------------------------------------------------|---------|
| `open`    | Ordine disponibile e in attesa di esecuzione     | ✅      |
| `full`    | Ordine eseguito                                  | 🔔      |
| *(vuoto)* | Ordine non ancora valutato (default iniziale)    | —       |

- Quando un ordine passa a `FULL`:
  - Viene mostrato un **toast**
  - Viene riprodotto un **suono**
  - Viene inviato un **messaggio Telegram** (se attivo)

---

## 🔁 Quando si aggiorna la tabella?

- Al cambio crypto (`select`)
- Quando clicchi su **Set Target Prices**
- Dopo ogni operazione `BUY` o `SELL` eseguita
- Ogni ~2–3 secondi (polling automatico)

---

## 💾 Dove sono salvati gli ordini?

I dati vengono salvati nel database `crypto_bot`, nella tabella `orders_{nome_crypto}`.  
Ogni crypto ha la propria tabella ordini autonoma (es: `orders_TAOBTC`, `orders_EIGENBTC`...).

> 📎 Questo consente di tenere separati i dati per ogni sessione attiva

---

## 📌 Colorazione speciale

Se attiva, la colonna **Target Price** può avere un colore verde tenue (es. `#d8e4c3`) per rendere più visibile il livello centrale.

> I toast vengono mostrati in basso a destra in stile mitologico (colori bronzo e pergamena, font Cinzel)

---

## 📋 Esempio di riga attiva

```
ID: level46
Order Level: 1
Target Price: 0.00001131
Order State: FULL
Actual Expense: 0.00016826
Gains: 0.09190
```

---

## 🛠 Azioni consigliate

- 🧪 Testa i livelli con piccoli `Base Expense` (es. 0.00001 BTC)
- 📈 Controlla la colonna `Actual Expense` per capire l’impatto del multiplicator
- 🔍 Ordina le colonne (opzionale: implementare funzione di ordinamento JS)
- 💬 Verifica che i messaggi Telegram corrispondano agli ordini eseguiti

---

## 🧠 Suggerimento

Per ogni crypto, mantieni:
- un numero ragionevole di livelli (es. 50–100)
- un polling moderato (2–5 secondi)
- un’attenzione ai margini di slippage

---

## 📌 Conclusione del Capitolo

Ora sai leggere e interpretare ogni colonna della tabella ordini di Emporion.  
Nel prossimo capitolo vedremo **come funzionano le notifiche visive, audio e Telegram**.

➡️ Prossimo capitolo: `Capitolo 6 – Notifiche e monitoraggio`

