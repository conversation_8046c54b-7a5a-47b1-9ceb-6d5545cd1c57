<?php 
include "conn.php";
session_start();

// Recupera dati
$user_id = $_SESSION['user_id'];
$data = json_decode(file_get_contents("php://input"), true);
$crypto = $data['crypto'] ?? '';
$slippage = $data['slippage_num_val'] ?? '';

// Valida
if ($slippage !== "" && is_numeric($slippage)) {
    $sql = "UPDATE crypto SET slippage_num = ? WHERE crypto_name = ? AND user = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("dsi", $slippage, $crypto, $user_id);

    if ($stmt->execute()) {
        echo json_encode(["status" => true]);
    } else {
        echo json_encode(["status" => false, "message" => "Errore DB"]);
    }

    $stmt->close();
} else {
    echo json_encode(["status" => false, "message" => "Valore non valido"]);
}
?>
