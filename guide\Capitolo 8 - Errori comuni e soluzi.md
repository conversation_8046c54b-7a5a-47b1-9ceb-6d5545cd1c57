# 📕 Capitolo 8 – Errori comuni e soluzioni

---

## 🎯 Obiettivo del capitolo

In questo capitolo vediamo **i problemi più frequenti** che puoi incontrare usando Emporion, con **cause, soluzioni e consigli pratici** per risolverli rapidamente.

---

## 🔧 1. <PERSON><PERSON><PERSON>: "Problem with API"

### ✅ Sintomi:
- Toast d’errore generico
- Ordini non vengono eseguiti
- Console mostra `Problem with API` da `functions.php`

### ❌ Causa probabile:
- Chiavi API Binance errate, mancanti o disattivate

### ✅ Soluzione:
1. Apri `functions.php`
2. Inserisci correttamente:
```php
$api_key = "LA_TUA_API_KEY";
$api_secret = "LA_TUA_SECRET_KEY";
```
3. Verifica che l’account Binance abbia permessi attivi
4. Se stai usando IP Whitelist, assicurati che `127.0.0.1` o il tuo IP pubblico sia incluso

---

## 🔌 2. Errore rete / Binance non raggiungibile

### ✅ Sintomi:
- Toast: ⚠️ Binance non raggiungibile
- Nessun aggiornamento dei prezzi
- Tabella ferma

### ❌ Causa:
- Internet assente o troppo lenta
- Binance API temporaneamente giù

### ✅ Soluzione:
- Controlla connessione e ping verso `https://api.binance.com`
- Riavvia `Apache` se usi XAMPP
- Aspetta il ripristino e verifica toast: `🔄 Connessione ripristinata`

---

## ❎ 3. Nessun ordine eseguito

### ✅ Sintomi:
- La tabella si aggiorna ma nessun ordine diventa `FULL`
- Anche con prezzi vicini al target

### ❌ Possibili cause:
- Slippage troppo basso (es. 0.1%)
- Prezzo attuale non realmente entro range
- Target Price errato o non coerente con attuale prezzo

### ✅ Soluzione:
- Aumenta lo `Slippage` al 1–2%
- Verifica `Digits` (es. 8 per BTC pairs)
- Usa `Actual Price` come riferimento reale

---

## 🔇 4. Toast o suoni non funzionano

### ❌ Causa:
- Bot non attivo
- Suono disabilitato nel browser
- Errore JS nel file `toast.js`

### ✅ Soluzione:
- Assicurati di cliccare **Start Bot**
- Apri Dev Tools (`F12`) → Console → vedi errori
- Verifica che `toast.js` sia incluso correttamente in `trade.php`

---

## 📭 5. Messaggi Telegram non arrivano

### ❌ Cause frequenti:
- Token o Chat ID errati
- Il bot Telegram non è stato avviato
- `sendMessage` bloccato per permessi errati

### ✅ Diagnosi:
- Vai a:  
  `https://api.telegram.org/bot<TUO_TOKEN>/getUpdates`  
  e verifica che il messaggio arrivi

### ✅ Soluzioni:
- Usa la modale **Configure Telegram Bot** per salvare correttamente i dati
- Se usi credenziali statiche in `trade.php`, verifica:
```php
$telegram_credentials = [
  'TAO/BTC' => [ 'chat_id' => '123...', 'token' => 'ABC...' ]
];
```

---

## 🔄 6. Il bot non si ferma

### ✅ Sintomi:
- Clic su **Stop Bot** non disattiva polling
- Toast o suoni continuano

### ✅ Soluzione:
- Verifica funzione `setEmporionStatus(false)`
- Assicurati che il valore `$_SESSION['emporion_status'][$crypto]` venga aggiornato
- Puoi forzare reload pagina con `location.reload();`

---

## 🛠 Strumenti per diagnosticare

| Strumento         | Come usarlo                         | Cosa mostra                        |
|------------------|--------------------------------------|------------------------------------|
| **Console JS**   | `F12` → `Console`                    | Errori JavaScript, Toast, fetch    |
| **Network tab**  | `F12` → `Network` → cerca `getPrice` | Stato polling Binance API          |
| **phpMyAdmin**   | http://localhost/phpmyadmin          | Contenuto tabelle `orders_...`     |
| **Telegram**     | API link Telegram `getUpdates`       | Ultimi messaggi ricevuti           |

---

## ✅ Suggerimenti preventivi

- Salva backup SQL regolarmente (`Save SQL Database`)
- Verifica che ogni crypto abbia:
  - almeno un target price
  - parametri base configurati
- Usa il test di Telegram al salvataggio delle credenziali
- Apri sempre Dev Tools se qualcosa sembra bloccato

---

## 📌 Conclusione del Capitolo

Emporion fornisce molti strumenti per identificare problemi, e con una buona configurazione iniziale è stabile e affidabile anche con molte sessioni attive.

➡️ Prossimo (e ultimo) capitolo: `Capitolo 9 – Appendici: FAQ, Glossario e Template`
