<?php
session_start();
include "config.php"; // Assicurati che il path sia corretto

$crypto = $_POST['crypto'] ?? '';

if (!$crypto || !isset($_SESSION['user_id'])) {
    http_response_code(400);
    echo "Parametro mancante o utente non loggato.";
    exit;
}

$userId = $_SESSION['user_id'];
$filename = "TradeTable_" . $crypto . "_" . date("Y-m-d_H-i-s") . ".csv";

// Crea la cartella Excel se non esiste
$folder = __DIR__ . '/../Excel';
if (!file_exists($folder)) {
    mkdir($folder, 0777, true);
}

$filepath = $folder . '/' . $filename;
$fp = fopen($filepath, 'w');

// Intestazioni CSV
$headers = ["Trade ID", "State", "Level", "Suggested Bid", "Bid", "Variation", "Target Price", "On Actual Price", "Ricavo", "Future Sell USDT", "Crypto Received"];
fputcsv($fp, $headers, ';'); // Usa punto e virgola per Excel Italiano

// Dati della tabella
$sql = "SELECT pricelevel, state_status, multiplicator, suggested_bid, bid, crypto_var, target_price, on_actual_price, ricavo, future_sell_usdt, crypto_received FROM trade WHERE crypto = ? AND user = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("ss", $crypto, $userId);
$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) {
    // Converti i numeri con virgola per Excel italiano
    $converted = array_map(function ($val) {
        return str_replace('.', ',', $val);
    }, $row);
    fputcsv($fp, $converted, ';');
}

fclose($fp);

// Ritorna solo il nome del file per JS
echo $filename;
