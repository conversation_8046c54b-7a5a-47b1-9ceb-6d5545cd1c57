<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => NULL,
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => NULL,
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'evenement/evenement' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => '0a16b0d71ab13284339abb99d9d2bd813640efbc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../evenement/evenement',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.6.1',
            'version' => '2.6.1.0',
            'reference' => 'be45764272e8873c72dbe3d2edcfdfcc3bc9f727',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'jaggedsoft/php-binance-api' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'd153dd9af258615d8b3c06c2a652387674e876b3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../jaggedsoft/php-binance-api',
            'aliases' => array(
                0 => '9999999-dev',
            ),
            'dev_requirement' => false,
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => 'e616d01114759c4c489f93b099585439f795fe35',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ratchet/pawl' => array(
            'pretty_version' => 'v0.4.1',
            'version' => '0.4.1.0',
            'reference' => 'af70198bab77a582b31169d3cc3982bed25c161f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ratchet/pawl',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ratchet/rfc6455' => array(
            'pretty_version' => 'v0.3.1',
            'version' => '0.3.1.0',
            'reference' => '7c964514e93456a52a99a20fcfa0de242a43ccdb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ratchet/rfc6455',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'react/cache' => array(
            'pretty_version' => 'v1.2.0',
            'version' => '1.2.0.0',
            'reference' => 'd47c472b64aa5608225f47965a484b75c7817d5b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'react/dns' => array(
            'pretty_version' => 'v1.11.0',
            'version' => '1.11.0.0',
            'reference' => '3be0fc8f1eb37d6875cd6f0c6c7d0be81435de9f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/dns',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'react/event-loop' => array(
            'pretty_version' => 'v1.4.0',
            'version' => '1.4.0.0',
            'reference' => '6e7e587714fff7a83dcc7025aee42ab3b265ae05',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/event-loop',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'react/promise' => array(
            'pretty_version' => 'v3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'c86753c76fd3be465d93b308f18d189f01a22be4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/promise',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'react/socket' => array(
            'pretty_version' => 'v1.14.0',
            'version' => '1.14.0.0',
            'reference' => '21591111d3ea62e31f2254280ca0656bc2b1bda6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/socket',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'react/stream' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'reference' => '6fbc9672905c7d5a885f2da2fc696f65840f4a66',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/stream',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
