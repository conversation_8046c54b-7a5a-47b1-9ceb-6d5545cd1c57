# 📘 Capitolo 1 – Introduzione

---

## 🔱 Cos’è Emporion?

**Emporion** è un bot di trading automatizzato con interfaccia web, progettato per operare su criptovalute tramite l’exchange **Binance**.  
È sviluppato in **PHP + JavaScript**, con uno stile visivo ispirato al mondo mitologico greco-romano.  

Si distingue per:

- ✅ Facilità d’uso tramite una dashboard visiva
- ✅ Gestione completamente automatizzata degli ordini su più crypto
- ✅ Parametri personalizzabili per ogni coppia (multiplicator, digits, slippage, base expense)
- ✅ Notifiche real-time via Telegram, toast e suoni mitologici
- ✅ Backup e ripristino rapidi del database

---

## 🎯 Obiettivi del progetto

Emporion nasce con l’obiettivo di:

- Automatizzare operazioni ripetitive e manuali nel trading crypto
- Consentire all’utente di impostare strategie a livelli (target price)
- Fornire un’interfaccia chiara, monitorata e personalizzabile
- Offrire **notifiche di esecuzione e anomalie**, anche da remoto via Telegram
- Fornire **strumenti di backup** e **esportazione tabella** facilmente accessibili

---

## 👤 A chi è rivolto?

Emporion è pensato per:

- Trader individuali che vogliono testare strategie automatizzate
- Sviluppatori che desiderano un progetto solido su cui estendere funzioni
- Chi ha familiarità con crypto, ma **poca esperienza di programmazione**

🔸 *Non è richiesta alcuna conoscenza avanzata di codice per utilizzare Emporion. Serve solo una conoscenza base di XAMPP e browser.*

---

## 📦 Cosa include Emporion

- ✅ Pagina `trade.php` con interfaccia completa
- ✅ Tabella ordini dinamica
- ✅ Gestione multipla di coppie crypto
- ✅ Salvataggio dei parametri per ogni crypto
- ✅ Telegram Bot integrato
- ✅ Notifiche visive (toast), sonore e su Telegram
- ✅ Esportazione in Excel
- ✅ Backup completo del database in formato `.sql`

---

## 🔍 Funzionalità principali

| Funzione                  | Descrizione                                                                 |
|---------------------------|-----------------------------------------------------------------------------|
| **Start / Stop Bot**      | Avvia o interrompe il bot per la crypto selezionata                         |
| **Target Prices**         | Imposta una lista manuale o genera automaticamente livelli multipli         |
| **Parametri personalizzati** | Ogni crypto ha i propri valori per multiplicator, digits, slippage, expense |
| **Telegram integrato**    | Invia notifiche automatiche per ordini, errori, avvio/stop del bot          |
| **Notifiche visive/sonore** | Toast informativi in stile mitologico, suoni di avviso per eventi chiave   |
| **Esportazione dati**     | Esporta la tabella ordini in `.xlsx` o salva/ripristina lo stato `.sql`     |

---

## 🧠 Prerequisiti tecnici

Per utilizzare Emporion, assicurati di avere a disposizione i seguenti componenti:

### Software richiesto:

| Requisito              | Dettaglio consigliato                                      |
|------------------------|------------------------------------------------------------|
| **Web server locale**  | [XAMPP](https://www.apachefriends.org/) con PHP 8.x + MySQL |
| **Browser moderno**    | Chrome, Firefox, Edge                                      |
| **Account Binance**    | API Key attiva con permessi `read` e `trade`               |
| **Telegram Bot**       | (Facoltativo) Creato tramite [@BotFather](https://t.me/BotFather) |
| **File system**        | Cartella `db/` e `Excel/` scrivibili dal server PHP         |

---

### 💻 Requisiti hardware consigliati

Dipendono dal **numero di sessioni attive** e dei **target price** per ciascuna:

#### Esempio pratico:
> 4 sessioni attive in parallelo, con 100 target prices ciascuna

| Risorsa            | Requisito consigliato                                        |
|--------------------|--------------------------------------------------------------|
| **CPU**            | Almeno Dual-core, consigliato Quad-core (es. i5/Ryzen 3+)    |
| **RAM**            | 4 GB minimo – 8 GB consigliati                               |
| **Spazio disco**   | ≥200 MB liberi (per log, backup SQL, export Excel)           |
| **Connessione**    | Stabile, con latenza <100ms verso `api.binance.com`          |

💡 Più target prices e più crypto = più chiamate a Binance → più carico sul browser e PHP.

---

### 📌 Scalabilità e ottimizzazioni

- Fino a **4 sessioni** e **100 TP per crypto** girano senza problemi su macchina desktop moderna
- Oltre le 5 crypto o >150 TP per ciascuna:
  - ⏱️ aumenta il polling interval a 5–10 secondi
  - 💾 riduci notifiche e log superflui
  - ☁️ valuta l’uso su VPS (opzionale) per uso continuo h24

---

## 🛡️ Sicurezza

- ❗ Le chiavi API Binance **non devono mai essere condivise o pubblicate**
- ✔️ Emporion è pensato per **uso locale** o su rete protetta
- 🔐 Le credenziali vengono archiviate in modo sicuro nel DB `crypto_bot`
- 📩 Le notifiche Telegram aiutano a **monitorare il bot da remoto** in sicurezza

---

## 🧭 Conclusione del Capitolo

Questo primo capitolo ti ha fornito:

- Una panoramica su Emporion
- A chi è destinato
- Cosa contiene
- Quali sono i requisiti per installarlo e farlo funzionare in modo fluido

➡️ Ora sei pronto per il **Capitolo 2 – Setup iniziale**, in cui vedremo passo dopo passo **come installare e lanciare il bot**.

