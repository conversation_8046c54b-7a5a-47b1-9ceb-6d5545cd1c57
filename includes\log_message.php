<?php
// FILE: includes/log_message.php

// Leggi il corpo JSON
$data = json_decode(file_get_contents("php://input"), true);

// Se mancano dati, esci
if (!$data || !isset($data["message"])) {
    http_response_code(400);
    echo json_encode(["status" => false, "message" => "Dati mancanti"]);
    exit;
}

$message = $data["message"];
$type = strtoupper($data["type"] ?? "INFO");

// Formatta riga di log
$logLine = "[" . date("Y-m-d H:i:s") . "][$type] $message" . PHP_EOL;

// Salva nel file log/bot.log
file_put_contents("../log/bot.log", $logLine, FILE_APPEND);

// Rispondi con successo
echo json_encode(["status" => true]);
?>
