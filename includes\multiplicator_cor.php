<?php 
include "conn.php";
session_start();

$user_id = $_SESSION['user_id'];
$crypto = $_POST['crypto']; // es. BTCUSDT
$multiplicator = $_POST['multiplicator_val'];

if ($multiplicator !== "" && is_numeric($multiplicator)) {
    $sql = "UPDATE crypto SET multiplicator = ? WHERE crypto_name = ? AND user = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("dsi", $multiplicator, $crypto, $user_id);

    if ($stmt->execute()) {
        header("Location:../trade.php");
    } else {
        header("Location:../trade.php?error=db");
    }

    $stmt->close();
} else {
    header("Location:../trade.php?error=input");
}
?>
