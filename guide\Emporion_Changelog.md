# Emporion Bot – Full Changelog

## Version 0.01
- **Base version received from freelancer @BraveBoyMJHNahid**

---

## Version 0.50 – July 14
- Updated the **style** of the Index and Trade pages, including modals, buttons, and text.
- Modified the **s_bid** formula with a fixed value: `const s_bid = 0.0001694800;`
- Updated the **NewBid** formula.
- Orders can now be placed with countervalues in **BTC and other coins**, not just USDT.
- Buy and Sell orders now fully functional.

---

## Version 0.51 – July 15
- Values for **slippage**, **multiplicator**, and **digits** are now visible next to their respective buttons.
- Added **“Set Base Expense”** button and modified DB to allow updating `s_bid` via the modal.

---

## Version 0.52 – July 15
- `s_bid`, slippage, multiplicator, and digits are now linked to each individual **crypto pair**, not to the user.
*(Migrated from `PersonalData` table → `Crypto` table)*

---

## Version 0.53 – July 16
- Replaced target price input with a **textarea** that automatically converts Excel Italian values (ascending order).
- Added a **search filter** for crypto pairs.
- Fixed minor error in **Set Base Expense** (direct DB edit via phpMyAdmin).
- Various frontend text adjustments.
- Updated `addtrade_cor` to implement **progressive Suggested Expense formula**:  
```php
$suggested_bid = $s_bid + pow($s_bid * 0.0125, $multiplicator)
```

---

## Version 0.54 – July 19
- Updated **table headers**.
- Added **Public IP** display.
- FIXED: Suggested Expense and Expense now update when **price level** increases.
- Added **toast messages**.

---

## Version 0.55 – July 20
- Fixed **buy and sell toast messages**.
- Added a **network reconnection toast**.
- Reduced disconnection spam (1 toast every 10 seconds).
- Added **Telegram bot button + modal + guide**.
- Added Telegram messages for **buy, sell, and disconnection**.
- Corrected the “level” shown in buy toast/message (was going 1→11 instead of 1→2).
- Fixed Suggested Bid formula.
- Added **internal alerts (toast?)**.

---

## Version 0.56 – July 21
- Unified style for **modals** and related buttons (Save & Close).
- Added buttons: **Configure Telegram Bot**, **Export Excel Table**, **Save SQL Database**, **Load SQL Database**.
- Recreated missing buttons and positioned them as per image “pulsanti completo”.
- Adjusted **Expense** formula to match Suggested Expense more closely (now correctly uses previous level’s suggested value).
- Added **“Emporion Open/Closed”** label.
- Corrected **Suggested Bid** value.
- Updated **button styling**.
- Fixed incorrect expense values in **Telegram messages**.
- Added Excel export function.
- Tested if cryptos beyond **USDT** and **USDC** could be used as secondary currencies.

---

## Version 0.57 – July 23
- Reduced & optimized **API key calls** to avoid bans.
- Updated Telegram messages (now working also for **errors, disconnections, start & stop events**).
- Configured **AnyDesk** for remote management via iPhone (`AnyVianel89! AnyVianel8989!`).

---

## Version 0.58 – July 24
- Fixed **Emporion is Open/Closed** message.
- Created **guides** for adding Telegram Bot credentials and chatbot setup.
- Tested links for various events (errors, executed orders, sales).
- Lost **SQL button styling**.

---

## Version 0.59 – July 24
- Improved button styling (almost finalized).
- Added **TXT user guide** with full instructions + tutorial.
- Added “Orders Levels” and “Delta” row.

---

## Version 0.60 – July 26
- Completed **Save SQL Database** and **Load SQL Database** functions.
- Notifications now **100% functional** – text still needs refining.

**Issues identified:**
1️⃣ **Set Base Expense modal** no longer updates DB value and text (first finding).  
2️⃣ On purchase, the **table and panel data** don’t reflect Binance order details (neither Expense nor Variation – first finding).

---

## Version 0.61 (BETA)
- Updated address book for 8 trades.

---

## Pending Items
- **Critical Issue**: On purchase, table/panel data still don’t match Binance order (Expense/Variation).
- Need to **test SELL** orders too (values are incorrect — test with real cases).
- Correct buy/sell **toast formulas** for revenue and related messages.
- Added **header** and updated **login styling**.
- **Move Telegram bot credentials** to a dedicated file.
- **Set Base Expense modal** still doesn’t update DB/text (*direct DB edits required for now*).

---

## Simplified Changes for Complex Issues
**NOTE:** To fix Telegram toast/messages, modify `toast.js`.

---

### 🔐 Telegram Bot Access Keys
- Unique bot credentials **per crypto pair** added directly into code.
- Updates reflected in **Save SQL Database** and **Load SQL Database**.
- Telegram notifications for **sell orders** now show **profit/loss variation** in $ (100%), split 50/50 between **Crypto 1** and **Crypto 2**.

```php
$telegram_credentials = [
    'TAO/BTC' => ['chat_id' => '344969533','token' => '7931286689:AAHqYDPnOm5Lqnh2KFsbvfwH_1C1XvAa5u4'],
    'EIGEN/BTC' => ['chat_id' => '344969533','token' => '7578760715:AAGxZDwpQwElC7wYkrD5vbtz30IIUriM5Zk'],
    'INJ/ETH' => ['chat_id' => '344969533','token' => '7909221687:AAGVI_4pzfehALòOUe2m2UIa8fAP_xMQy74s'],
    'ENA/BNB' => ['chat_id' => '344969533','token' => '8480031264:AAHYwsvH5B8kKcAqNWJgx_R31M6SO-beWKE'],
    'AAVE/ETH' => ['chat_id' => '344969533','token' => '7980826677:AAG7BgYkxKb_UukKLkPDUw65W4rSE3rowNM'],
    'RENDER/BTC' => ['chat_id' => '344969533','token' => '8397676009:AAFMtppfJJKm_XJwxj0TL6Zv61d4gjp5bFw'],
    'TIA/BTC' => ['chat_id' => '344969533','token' => '7751486661:AAGrqRKzSbTplbXQrOX6cFKX09OEXP5Dao8'],
    'LINK/ETH' => ['chat_id' => '344969533','token' => ''],
    'SUI/BNB' => ['chat_id' => '344969533','token' => '8332488211:AAFvIF_F4Iznls478yM8v04NbVheqCy1-Kk'],
    'FET/BNB' => ['chat_id' => '344969533','token' => '7684899162:AAEAXyJwI4Dvs7BPqfXr7wCrHHR_iomublo']
];
```
