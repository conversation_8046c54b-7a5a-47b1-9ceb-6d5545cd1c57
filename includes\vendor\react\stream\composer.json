{"name": "react/stream", "description": "Event-driven readable and writable streams for non-blocking I/O in ReactPHP", "keywords": ["event-driven", "readable", "writable", "stream", "non-blocking", "io", "pipe", "ReactPHP"], "license": "MIT", "authors": [{"name": "<PERSON>", "homepage": "https://clue.engineering/", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://wyrihaximus.net/", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://sorgalla.com/", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://cboden.dev/", "email": "<EMAIL>"}], "require": {"php": ">=5.3.8", "react/event-loop": "^1.2", "evenement/evenement": "^3.0 || ^2.0 || ^1.0"}, "require-dev": {"phpunit/phpunit": "^9.5 || ^5.7 || ^4.8.35", "clue/stream-filter": "~1.2"}, "autoload": {"psr-4": {"React\\Stream\\": "src/"}}, "autoload-dev": {"psr-4": {"React\\Tests\\Stream\\": "tests/"}}}