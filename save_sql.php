<?php
// save_sql.php – Crea un backup del database crypto_bot e lo salva nella cartella /db

$host = "localhost";
$user = "root";
$password = ""; // metti la password MySQL se necessaria
$dbname = "crypto_bot";

// Connessione al database
$conn = new mysqli($host, $user, $password, $dbname);
if ($conn->connect_error) {
    die("❌ Connessione fallita: " . $conn->connect_error);
}

// Recupera tutte le tabelle
$tables = [];
$result = $conn->query("SHOW TABLES");
while ($row = $result->fetch_array()) {
    $tables[] = $row[0];
}

// Genera il contenuto SQL (struttura + dati)
$sqlScript = "";
foreach ($tables as $table) {
    // Struttura tabella
    $res = $conn->query("SHOW CREATE TABLE `$table`");
    $row = $res->fetch_assoc();
    $sqlScript .= "\n\nDROP TABLE IF EXISTS `$table`;\n";
    $sqlScript .= $row['Create Table'] . ";\n\n";

    // Dati tabella
    $res = $conn->query("SELECT * FROM `$table`");
    while ($r = $res->fetch_assoc()) {
        $columns = array_keys($r);
        $values  = array_map([$conn, 'real_escape_string'], array_values($r));
        $sqlScript .= "INSERT INTO `$table` (`" . implode("`,`", $columns) . "`) VALUES ('" . implode("','", $values) . "');\n";
    }
}

// Crea la cartella /db se non esiste
$backupDir = __DIR__ . '/db';
if (!file_exists($backupDir)) {
    mkdir($backupDir, 0777, true);
}

// Salva il file
$filename = $backupDir . '/crypto_bot_' . date('Y-m-d_H-i-s') . '.sql';
file_put_contents($filename, $sqlScript);

// Messaggio finale
if (file_exists($filename)) {
    echo "✅ Backup completato! File salvato in: db/" . basename($filename);
} else {
    echo "❌ Errore durante il salvataggio del backup!";
}
?>
