{"name": "react/promise", "description": "A lightweight implementation of CommonJS Promises/A for PHP", "license": "MIT", "authors": [{"name": "<PERSON>", "homepage": "https://sorgalla.com/", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://clue.engineering/", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://wyrihaximus.net/", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://cboden.dev/", "email": "<EMAIL>"}], "require": {"php": ">=7.1.0"}, "require-dev": {"phpstan/phpstan": "1.10.20 || 1.4.10", "phpunit/phpunit": "^9.5 || ^7.5"}, "autoload": {"psr-4": {"React\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "autoload-dev": {"psr-4": {"React\\Promise\\": ["tests/fixtures/", "tests/"]}, "files": ["tests/Fiber.php"]}, "keywords": ["promise", "promises"]}