<?php 
include "conn.php";
include "functions.php";

session_start();

// 🟢 Imposta header JSON per evitare errori JS
header('Content-Type: application/json');

$function = new Functions();
$jsonData = file_get_contents("php://input");
$data = json_decode($jsonData);

$dateTime = Date("m-d-Y");

// 🔵 ORDINE DI ACQUISTO TRAMITE API
$order = $function->buyUSDT($data->crypto, $data->bid, $data->actualPrice);

if (!empty($order)) {

    // ✅ Valore totale speso (quote)
    $usdtValue = ($order['cummulativeQuoteQty'] == 0) 
        ? $data->actualPrice * $order['origQty'] 
        : $order['cummulativeQuoteQty'];

    // ✅ Quantità acquistata (crypto base)
    $qty_bought = (float)$order['origQty'];

    // ✅ Prezzo di acquisto
    $buy_price = (float)$data->actualPrice;

    // ✅ Aggiorna trade table
    $s_bid = floatval($data->s_bid);
    $multiplicator = floatval($data->multiplicator);
    $pricelevel = intval($data->pricelevel);

    $suggested_bid = $s_bid * pow($multiplicator, $pricelevel);
    $suggested_bid = number_format($suggested_bid, 10, '.', '');

    $sql = "UPDATE `trade` SET 
                `state_status` = '$data->state_status',
                `multiplicator` = '$data->multiplicator',
                `suggested_bid` = '$suggested_bid',
                `bid` = '{$usdtValue}',
                `crypto_var` = '{$order['origQty']}',
                `on_actual_price` = '0',
                `ricavo` = '0',
                `future_sell_usdt` = '{$usdtValue}',
                `crypto_received` = '0' 
            WHERE 
                `pricelevel` = '$data->pricelevel' AND 
                `crypto` = '$data->crypto' AND 
                `user` = '{$_SESSION['user_id']}'";

    $runQuery = mysqli_query($conn, $sql);
    if (!$runQuery) {
        echo json_encode([
            "status" => false,
            "message" => "Errore SQL (update trade BUY)",
            "error" => mysqli_error($conn)
        ]);
        exit;
    }

    // ✅ Recupera crypto posseduta prima
    $get_lasted_possessed_crypto = "
        SELECT possessed_crypto FROM `history` 
        WHERE crypto = '$data->crypto' 
          AND `user` = '{$_SESSION['user_id']}' 
        ORDER BY history_id LIMIT 1";

    $runQuery_possessed_crypto = mysqli_query($conn, $get_lasted_possessed_crypto);
    if (!$runQuery_possessed_crypto) {
        echo json_encode([
            "status" => false,
            "message" => "Errore DB: select history",
            "error" => mysqli_error($conn)
        ]);
        exit;
    }

    $result = mysqli_fetch_all($runQuery_possessed_crypto, MYSQLI_ASSOC);
    $add_possessed_crypto = !empty($result) ? $result[0]['possessed_crypto'] : 0;

    // ✅ Aggiorna possesso
    $total_possessed_crypto = $qty_bought + $add_possessed_crypto;

    // ✅ Valore attuale in USD (solo indicativo)
    $actual_val = $data->actualPrice * $total_possessed_crypto;

    // ✅ Inserisci in history
    $sql2 = "INSERT INTO `history`(
                `history_date`, `operation`, `usdt_value`, 
                `crypto_var`, `possessed_crypto`, `buy_sell_price`, 
                `actual_value`, `crypto`, `user`
            ) VALUES (
                '$dateTime','$data->action','{$usdtValue}',
                '{$order['origQty']}','$total_possessed_crypto',
                '$buy_price', '$actual_val', 
                '$data->crypto','{$_SESSION['user_id']}'
            )";

    $run_query_sql2 = mysqli_query($conn, $sql2);
    if (!$run_query_sql2) {
        echo json_encode([
            "status" => false,
            "message" => "Errore DB: insert history",
            "error" => mysqli_error($conn)
        ]);
        exit;
    }

    // 🔍 Estrazione base/quote (es. ENA/BNB)
    if (strpos($data->crypto, "/") !== false) {
        list($baseAsset, $quoteAsset) = explode("/", $data->crypto);
    } else {
        // fallback per coppie tipo ENABNB
        $baseAsset = substr($data->crypto, 0, -3);
        $quoteAsset = substr($data->crypto, -3);
    }

    // ✅ Recupera il prezzo in USD della quote (es. BNB/USDT)
    $quoteAssetPriceUSD = 0;
    $quoteSymbol = $quoteAsset . "USDT"; // es. BNB -> BNBUSDT

    try {
        $binancePriceApi = file_get_contents("https://api.binance.com/api/v3/ticker/price?symbol={$quoteSymbol}");
        if ($binancePriceApi) {
            $priceData = json_decode($binancePriceApi, true);
            $quoteAssetPriceUSD = isset($priceData['price']) ? (float)$priceData['price'] : 0;
        }
    } catch (Exception $e) {
        $quoteAssetPriceUSD = 0;
    }

    // 🔢 Calcolo campi toast usando il prezzo reale della quote
    $qty_usd = $qty_bought * $buy_price * $quoteAssetPriceUSD;
    $spesa_usd = $usdtValue * $quoteAssetPriceUSD;

    // ✅ RISPOSTA COMPLETA PER TOAST
    echo json_encode([
        "status" => true,
        "trade_id" => $data->pricelevel,
        "target_price" => $buy_price,
        "spesa" => $usdtValue,
        "spesa_usd" => number_format($spesa_usd, 2),
        "qty" => $qty_bought,
        "qty_usd" => number_format($qty_usd, 2),
        "quote" => $quoteAsset,
        "base" => $baseAsset,
        "level_from" => $pricelevel,
        "level_to" => $pricelevel + 1
    ]);

} else {
    echo json_encode([
        "status" => false,
        "massage" => "Problem with api",
        "data" => $order
    ]);
}

mysqli_close($conn);
?>
