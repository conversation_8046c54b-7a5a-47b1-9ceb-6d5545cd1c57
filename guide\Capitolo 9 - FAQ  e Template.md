# 📘 Capitolo 9 – Appendici: FAQ, Glossario e Template

---

## ❓ FAQ – Domande frequenti

---

### 🔸 "Ho avviato il bot ma non succede nulla"

✅ Controlla:
- Che una crypto sia selezionata
- Che la tabella sia popolata con Target Prices
- Che il prezzo attuale si stia aggiornando
- Che `Base Expense` sia impostata

---

### 🔸 "Emporion mi segnala errore Binance, ma internet funziona"

✅ Cause possibili:
- Binance ha un blocco temporaneo
- La tua chiave API è scaduta o errata
- Hai superato il limite di chiamate

✅ Soluzione:
- Attendi qualche secondo (Emporion riprova da solo)
- Verifica chiavi nel file `functions.php`
- Riduci la frequenza di polling (da 2s a 5s)

---

### 🔸 "Telegram non manda messaggi, ma tutto sembra OK"

✅ Controlla:
- Token e Chat ID inseriti correttamente
- Il bot è stato avviato su Telegram (premuto Start)
- Hai inserito le credenziali nel posto giusto (modale o `trade.php`)
- Fai un test manuale da browser:
  ```
  https://api.telegram.org/bot<TUO_TOKEN>/sendMessage?chat_id=<CHAT_ID>&text=test
  ```

---

### 🔸 "Come faccio a pulire tutto e ricominciare?"

Esegui da phpMyAdmin:

```sql
DROP DATABASE IF EXISTS crypto_bot;
CREATE DATABASE crypto_bot;
```

Poi clicca su **Load SQL Database** per caricare un backup pulito.

---

### 🔸 "Posso usare Emporion su hosting online?"

Sì, ma:
- Il tuo hosting deve supportare PHP + MySQL
- Serve HTTPS se usi Telegram
- I polling continui verso Binance possono superare i limiti → meglio usare VPS

---

## 🧾 Glossario dei termini

| Termine         | Significato                                                        |
|-----------------|---------------------------------------------------------------------|
| **TP**          | Target Price, il prezzo bersaglio di acquisto o vendita            |
| **Slippage %**  | Tolleranza massima tra prezzo atteso e prezzo reale                |
| **Order Level** | Numero relativo (positivo o negativo) che rappresenta un gradino   |
| **Base Expense**| Importo iniziale speso in Crypto 2 per il livello centrale         |
| **Multiplicator**| Fattore di crescita tra un livello e il successivo                |
| **Digits**      | Quanti decimali usare per calcoli e prezzi                         |
| **FULL**        | Ordine eseguito                                                    |
| **OPEN**        | Ordine in attesa                                                   |
| **Emporion Open**| Stato attivo del bot per una crypto selezionata                   |

---

## 📑 Template utili

---

### 📜 Telegram config (`trade.php`)

```php
$telegram_credentials = [
  'TAO/BTC' => [
    'chat_id' => '123456789',
    'token' => '123456789:ABCDEF_YourTokenHere'
  ]
];
```

---

### 🧮 Esempio Target Price manuale (in modale)

```
0.00001100
0.00001130
0.00001160
0.00001190
0.00001220
```

---

### 🔧 Comando per forzare reset configurazione crypto

```sql
DELETE FROM crypto WHERE user = 1;
```

Oppure:

```sql
TRUNCATE TABLE crypto;
```

---

### 💾 Directory struttura progetto

```
bot/
├── trade.php
├── includes/
│   ├── functions.php
│   └── conn.php
├── js/
│   └── action.js
├── img/
│   └── cornucopia.jpg
├── db/
│   └── backup_*.sql
├── Excel/
│   └── exported_table.xlsx
```

---

## 🧠 Suggerimenti finali

- Usa **naming coerente** per le tue crypto (es. `FET/BNB`, `TAO/BTC`)
- Salva spesso, esporta dopo ogni giornata operativa
- Testa sempre i nuovi livelli con importi ridotti
- Documenta le tue strategie in un file `.md` separato
- Non lasciare il bot attivo se non monitorato con Telegram

---

## 🏁 Fine guida

Hai completato la guida ufficiale di **Emporion**.

👉 Sei ora in grado di:

- Installare, configurare e usare il bot
- Monitorare l’attività delle crypto
- Reagire agli errori
- Personalizzare il tuo flusso operativo

💬 Se hai bisogno di aggiunte, personalizzazioni, o vuoi integrare funzionalità avanzate (log, grafici, webhook...), puoi farlo a partire da questa base solida.

Buon trading mitologico! ⚡

