
/* === STILI PERSONALIZZATI TRADE.PHP === */

/* Pulsanti: colori personalizzati */
.btn-warning {
  background-color: #bfa06a;
  border-color: #a98b4f;
  color: #4b2e0e;
}
.btn-warning:hover {
  background-color: #bfa06a;
  border-color: #a98b4f;
}

.btn-primary {
  background-color: #4b7ca4;
  border-color: #3e6d91;
  color: white;
}
.btn-primary:hover {
  background-color: #6b9cc1;
  border-color: #5a87aa;
}

.btn-success {
  background-color: #6c8b4e;
  border-color: #5a7741;
  color: white;
}
.btn-success:hover,
.btn-success:active {
  background-color: #85a764;
  border-color: #6c8b4e;
}

.btn-secondary {
  background-color: #c2a77e;
  border-color: #ad9361;
  color: #4b2e0e;
}
.btn-secondary:hover {
  background-color: #ad9361;
  border-color: #997b54;
}

.btn-brown {
  background-color: #7a4e2c;
  border-color: #6a4325;
  color: white;
}
.btn-brown:hover {
  background-color: #8e5a32;
  border-color: #7a4e2c;
}

.btn-logout {
  background-color: #a14545;
  border-color: #8a3b3b;
  color: white;
}
.btn-logout:hover {
  background-color: #bb5c5c;
  border-color: #a14545;
}

/* Nascondi completamente i due box */
.highest_priceTarget,
.lowest_priceTarget {
  display: none !important;
  visibility: hidden !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Ordina e imposta griglia per i pulsanti */
.button-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin: 30px auto;
  max-width: 900px;
}

.button-grid .btn {
  width: 100%;
  font-size: 13px;
  padding: 10px;
}

/* Responsive: riga singola su mobile */
@media (max-width: 768px) {
  .button-grid {
    grid-template-columns: 1fr;
  }
}
