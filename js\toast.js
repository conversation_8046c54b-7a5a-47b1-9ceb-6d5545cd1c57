// ✅ FILE: js/toast.js
// Questo file gestisce tutti i toast, visivi e Telegram

// 📦 Crea container una volta sola
if (!document.getElementById("toast-container")) {
  const container = document.createElement("div");
  container.id = "toast-container";
  container.style.position = "fixed";
  container.style.bottom = "20px";
  container.style.right = "20px";
  container.style.zIndex = "9999";
  container.style.maxWidth = "350px";
  document.body.appendChild(container);
}

function showToast(type, data) {
  const toast = document.createElement("div");
  toast.classList.add("toast-box");

  // 🎨 Colori base
  toast.style.background =
    type === "error" ? "#ffdddd" :
    type === "sell" ? "#d1ecf1" :
    type === "network" ? "#fff3cd" : // giallo tenue per avviso
    "#d4edda"; // verde di default per successi

  toast.style.borderLeft =
    type === "error" ? "6px solid #e53935" :
    type === "sell" ? "6px solid #0c5460" :
    type === "network" ? "6px solid #b38f00" : // giallo scuro per disconnessione
    "6px solid #155724";

  toast.style.color = "#333";
  toast.style.padding = "10px 15px";
  toast.style.marginTop = "10px";
  toast.style.borderRadius = "8px";
  toast.style.boxShadow = "0 2px 6px rgba(0,0,0,0.2)";
  toast.style.fontSize = "14px";
  toast.style.lineHeight = "1.4";

  // ✨ Contenuto dinamico
  let content = "";

  if (type === "buy") {
    const from = Number(data.level_from);
    const to = Number(data.level_to);

    content = `📈 Ordine di acquisto eseguito<br>
• Trade ID: ${data.trade_id}<br>
• Target price: ${data.target_price}<br>
• Spesa: ${data.spesa} ${data.quote} (~${data.spesa_usd} USD)<br>
• Quantità acquistata: ${data.qty} ${data.base} (~${data.qty_usd} USD)<br>
• Level: ${from} → ${to}`;

    // 📲 Telegram
    sendToTelegram("buy", data);

  } else if (type === "sell") {
    content = `📉 Ordine di vendita eseguito<br>
• Trade ID: ${data.trade_id}<br>
• Target price: ${data.target_price}<br>
• Quantità venduta: ${data.qty} ${data.base} (~${data.qty_usd} USD)<br>
• Ricavo netto: +${data.ricavo_usd} USD<br>
• Guadagno diviso: ${data.usdt_half} ${data.quote} (~${data.usdt_half_usd} USD) + ${data.crypto_half} ${data.base} (~${data.crypto_half_usd} USD)`;

    // 📲 Telegram
    sendToTelegram("sell", data);

  } else if (type === "error") {
    content = `❌ <b>${data.title || "Errore"}</b><br>${data.message}`;

    // 📲 Telegram
    sendToTelegram("error", {
      title: data.title || "Errore",
      message: data.message
    });

  } else if (type === "network") {
    // 🚨 DISCONNESSIONE
    content = `⚠️ <b>Connessione persa</b><br>Emporion ha perso la connessione a Binance.`;
    sendToTelegram("disconnected", { crypto: currentCrypto });

  } else if (type === "success" && data.title === "✅ Connessione ristabilita") {
    // 🔌 RICONNESSIONE
    content = `🔌 <b>Connessione ristabilita</b><br>Emporion ha ripreso la connessione con Binance.`;
    sendToTelegram("reconnected", { crypto: currentCrypto });

  } else {
    // 📝 MESSAGGI GENERICI
    content = `<b>${data.title || "Notifica"}</b><br>${data.message}`;
  }

  // 📥 Mostra il toast a schermo
  toast.innerHTML = content;
  document.getElementById("toast-container").appendChild(toast);

  // ⏳ Rimuovi dopo 6 secondi
  setTimeout(() => toast.remove(), 6000);
}
