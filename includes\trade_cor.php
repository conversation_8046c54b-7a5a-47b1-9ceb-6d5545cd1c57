<?php 
    include "conn.php";
    session_start();

    header('Content-Type: application/json');
    ini_set('display_errors', 1);
    error_reporting(E_ALL);

    if (!isset($_SESSION['user_id'])) {
        echo json_encode(["status" => false, "message" => "Sessione utente non valida"]);
        exit;
    }

    $jsonData = file_get_contents("php://input");
    $data = json_decode($jsonData);

    if (!isset($data->currency) || empty($data->currency)) {
        echo json_encode(["status" => false, "message" => "Nessuna crypto selezionata"]);
        exit;
    }

    $crypto = mysqli_real_escape_string($conn, $data->currency);

    // ✅ Recupera i dati della tabella trade
    $sql = "SELECT * FROM trade WHERE crypto = '$crypto' AND `user` = '{$_SESSION['user_id']}' ORDER BY trade_id DESC";
    $runQuery = mysqli_query($conn, $sql);
    if (!$runQuery) {
        echo json_encode([
            "status" => false,
            "message" => "Errore nella query trade",
            "error" => mysqli_error($conn)
        ]);
        exit;
    }
    $rows = mysqli_fetch_all($runQuery, MYSQLI_ASSOC);

    // ✅ Recupera s_bid + multiplicator dalla tabella crypto
    $s_bid_sql = "SELECT s_bid, multiplicator FROM crypto WHERE crypto_name = '$crypto' AND user = '{$_SESSION['user_id']}' LIMIT 1";
    $s_bid_result = mysqli_query($conn, $s_bid_sql);
    $s_bid_row = mysqli_fetch_assoc($s_bid_result);
    $s_bid = isset($s_bid_row['s_bid']) ? floatval($s_bid_row['s_bid']) : 0.0;
    $multiplicator_crypto = isset($s_bid_row['multiplicator']) ? floatval($s_bid_row['multiplicator']) : 1.0;

    // 🔁 Calcolo solo di suggested_bid
    foreach ($rows as &$row) {
        $multiplicator_trade = floatval($row['multiplicator']);  // esponente
        $row['suggested_bid'] = number_format($s_bid * pow($multiplicator_crypto, $multiplicator_trade), 10, '.', '');
        // ⚠️ NON modifichiamo bid! Rimane quello nel database
    }

    echo json_encode($rows);
    mysqli_close($conn);
?>
