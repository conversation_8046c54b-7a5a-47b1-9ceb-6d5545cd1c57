# 📗 Capitolo 2 – Setup iniziale

---

## ⚙️ Obiettivo del capitolo

In questo capitolo vedremo **come installare e avviare Emporion** sul tuo computer, passo dopo passo.  
Alla fine della sezione sarai in grado di aprire la dashboard `trade.php`, aggiungere le tue crypto e configurare i parametri base.

---

## 🧰 1. Requisiti tecnici (riassunto operativo)

- ✅ [XAMPP](https://www.apachefriends.org/it/index.html) (server Apache + PHP + MySQL)
- ✅ Browser moderno (Chrome o Firefox)
- ✅ File di Emporion nella cartella `htdocs` (es. `C:/xampp/htdocs/bot/`)
- ✅ Cartelle:
  - `includes/` – script PHP
  - `img/` – immagini estetiche
  - `js/` – JavaScript frontend
  - `db/` – salvataggi SQL
  - `Excel/` – esportazioni tabella
- ✅ Database MySQL chiamato `crypto_bot`
- ✅ Credenziali API Binance attive
- ✅ (Facoltativo) Token e Chat ID del tuo bot Telegram

---

## 📁 2. Installazione dei file

### 🧱 Passaggi:

1. **Installa XAMPP**  
   Scarica da: https://www.apachefriends.org/  
   Avvia `Apache` e `MySQL` da `xampp-control.exe`

2. **Copia la cartella del progetto**  
   Posiziona tutto il contenuto di Emporion (file e sottocartelle) in:  
   `C:/xampp/htdocs/bot/`

3. **Crea le cartelle se non esistono**:
   - `db/`
   - `Excel/`
   - `img/`
   - `includes/`
   - `js/`

4. **Verifica l’accesso dal browser:**  
   Vai su:  
   `http://localhost/bot/trade.php`

---

## 🧮 3. Importazione del database

### Metodo 1 – via phpMyAdmin (consigliato):

1. Accedi a: `http://localhost/phpmyadmin`
2. Clicca su **Nuovo** → crea database chiamato `crypto_bot`
3. Importa il file `.sql` (ad esempio `db/backup_YYYYMMDD.sql`)

### Metodo 2 – automatico (opzionale):

Puoi cliccare su **"Load SQL Database"** nella pagina `trade.php` per caricare manualmente un file `.sql` dal tuo PC e sovrascrivere il database.

---

## 🛠️ 4. Configurazione iniziale

### A) API Binance

Le chiavi API sono da inserire nel backend (normalmente nel file `functions.php` o `conn.php`).

Verifica che siano correttamente inserite come:

```php
$api_key = "YOUR_BINANCE_API_KEY";
$api_secret = "YOUR_BINANCE_API_SECRET";
```

> 🛡️ **Non condividere mai queste chiavi.**

---

### B) Telegram (facoltativo ma consigliato)

1. Crea il tuo bot con [@BotFather](https://t.me/BotFather)
2. Salva il **token** fornito da Telegram
3. Invia un messaggio al bot da Telegram
4. Visita:
   ```
   https://api.telegram.org/bot<YOUR_TOKEN>/getUpdates
   ```
   e copia il tuo `chat_id`
5. Inserisci token e chat ID in Emporion usando:
   - Il pulsante **"Configure Telegram Bot"**
   - Oppure preimpostali per le crypto direttamente nel file `trade.php`

---

### C) Aggiungi le tue crypto

Clicca su **“Add/Remove Crypto Pair”**, seleziona una coppia (es. `TAOBTC`) e premi **Add**.  
Le crypto saranno salvate nel database con parametri indipendenti.

---

## ✅ 5. Avvio iniziale

Dopo aver aggiunto almeno una crypto:

1. Selezionala dal menu a tendina
2. Imposta i parametri di partenza (Base Expense, Multiplicator, ecc.)
3. Clicca su **Set Target Prices** e incolla o genera i tuoi livelli
4. Premi **Start Bot**  
5. Il sistema inizierà a monitorare e piazzare ordini automaticamente

---

## 🎉 Verifica corretto funzionamento

Dopo l’avvio:

- Il messaggio **"Emporion is Open"** appare
- La tabella si popola e aggiorna automaticamente
- I prezzi si aggiornano
- I toast e suoni si attivano se un ordine viene eseguito
- Ricevi eventuali messaggi Telegram

---

## 🔍 In caso di problemi

Consulta il [Capitolo 7 – Errori comuni e soluzioni](#)  
Oppure apri la console del browser (`F12`) per vedere eventuali errori JavaScript o PHP.

---

## 📌 Conclusione del Capitolo

Hai ora installato e avviato correttamente Emporion sul tuo sistema.  
Nel prossimo capitolo vedremo **in dettaglio l’interfaccia utente**, con tutte le sue sezioni, pulsanti, parametri e funzioni.

➡️ Prossimo capitolo: `Capitolo 3 – Interfaccia di controllo`

