<?php
session_start();
include "conn.php";

$data = json_decode(file_get_contents("php://input"), true);
$crypto = $data["crypto"];
$multiplicator = $data["multiplicator"];

$sql = "UPDATE crypto SET multiplicator = ? WHERE crypto_name = ? AND user = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("dss", $multiplicator, $crypto, $_SESSION['user_id']);
$stmt->execute();

echo json_encode(["status" => true]);
?>
