<?php 
include "conn.php";
session_start();

$user_id = $_SESSION['user_id'];
$crypto = $_POST['crypto'];
$digit = $_POST['digit_num_val'];

if ($digit !== "" && is_numeric($digit)) {
    $sql = "UPDATE crypto SET digit_num = ? WHERE crypto_name = ? AND user = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("isi", $digit, $crypto, $user_id);

    if ($stmt->execute()) {
        header("Location:../trade.php");
    } else {
        header("Location:../trade.php?error=db");
    }

    $stmt->close();
} else {
    header("Location:../trade.php?error=input");
}
?>
