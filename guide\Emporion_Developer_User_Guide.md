# Emporion Bot – Comprehensive Guide for Users & Developers

## 📖 1. Introduction
Emporion is a **web-based crypto trading bot** styled with a **Greek-Roman mythological theme**. It integrates with **Binance API** for trading and **Telegram Bot API** for real-time notifications.  

The bot allows you to:
- Set and manage **Target Prices** for multiple crypto pairs.
- Automatically execute **buy and sell orders** based on those prices.
- Receive **browser toast notifications** and **Telegram messages** for important events (order filled, errors, disconnections).
- Manage **SQL database backups** and restore operations directly from the interface.
- Customize **per-pair trading parameters** (Base Expense, Slippage, Digits, Multiplicator).
- Export trade history to **Excel** for record-keeping.

This guide explains the **program structure**, **how the system works**, and highlights **what needs to be fixed or improved**.

---

## 📂 2. Project Structure (ZIP Content)

Inside the ZIP (`Emporion 0.60`), you will find:

### ✅ **Core PHP Files**
- **index.php** → Login page.
- **trade.php** → Main dashboard where trading happens.
- **users.php / add-user.php** → Basic user management.

### ✅ **Directories**
- **/includes/** – Core PHP scripts that handle:
  - DB updates (e.g., `update_sbid.php`, `updatetradebuy_cor.php`, `updatetradesell_cor.php`)
  - Telegram communication (`send_telegram.php`)
  - Binance integration
- **/js/** – Frontend logic:
  - `action.js` → Handles Binance price polling, toast messages, Telegram notifications, and UI updates.
  - `toast.js` → Displays styled toast notifications.
- **/css/** – All styling:
  - `style.css`, `login.css`, `trade_custom_buttons.css`
- **/db/** – Database dumps & backups:
  - `crypto_bot.sql` (main schema)
- **/guide/** – Existing Markdown user guide chapters.
- **/log/** – Logs from the bot’s activity.
- **/img/** – Images for the UI and styling.
- **/assets/** – Sound file for alerts (`sound.mp3`).

---

## ⚙️ 3. How the Bot Works

### 🔑 **Step 1 – Login**
- User accesses `index.php`, logs in, and is redirected to `trade.php`.

### 🔄 **Step 2 – Select Crypto Pair**
- A dropdown menu allows selection of a crypto pair (e.g., TAO/BTC).
- When selected, the bot loads **all parameters for that specific pair** from the database:
  - `s_bid` (starting bid)
  - `slippage`
  - `multiplicator`
  - `digit_num`
  - Telegram credentials (token & chat_id)

### ⚙️ **Step 3 – Set Parameters**
- Modals for:
  - **Set Base Expense**
  - **Set Multiplicator**
  - **Set Digits**
  - **Set Slippage**

Parameters are saved to the `crypto` table **per pair**, not globally.

### 🎯 **Step 4 – Add Target Prices**
- User pastes target prices into a modal **textarea**.
- Prices are converted (Italian Excel → standard format) and sorted ascending.
- Orders populate dynamically in the **Trade Table**.

### 📈 **Step 5 – Bot Starts Trading**
- Press **Start Bot**:
  - `action.js` starts polling Binance API for live prices.
  - When price hits a **Target Price**:
    - PHP backend sends an order to Binance.
    - On success → updates DB, triggers toast, Telegram message, and plays alert sound.

### 🏦 **Step 6 – Backup / Restore**
- **Save SQL Database**: Dumps DB to `/db/` folder.
- **Load SQL Database**: Replaces current DB with uploaded `.sql`.

---

## 🚨 4. Known Issues (from Changelog)

### ❌ **1. Base Expense Modal Not Updating DB**
- The “Set Base Expense” modal no longer saves changes to DB or updates displayed value.

### ❌ **2. Mismatch Between Orders and Table Data**
- When a buy order executes:
  - Table shows wrong **expense** and **variation** (not matching Binance order details).
- SELL orders need similar verification (values may also be wrong).

### ❌ **3. Toast and Telegram Messages**
- Some profit/loss calculations are incorrect or inconsistent between toast and Telegram.

### ❌ **4. Telegram Credentials**
- Hardcoded inside `trade.php` and `action.js`.
- Should be moved to:
  - A **secure PHP config file**
  - Or stored in DB (already partially implemented).

### ❌ **5. SQL Button Styling**
- Button styling was lost in previous versions; must be fixed for UI consistency.

---

## 🛠️ 5. Developer Notes – Where to Work

### 💻 **Frontend**
- **trade.php** – Heavy inline code; should be cleaned and modularized.
- **action.js** – Critical for price polling and event handling.
  - Consider **optimizing polling** to avoid Binance API bans.
  - Improve error handling for disconnections (currently toast spam).

### 🖥 **Backend**
- **includes/** – Scattered procedural PHP files.
  - Merge into **logical classes** (e.g., `BinanceService`, `TelegramService`).
- **functions.php** – Has reusable functions but lacks clear separation.

### 🗄 **Database**
- `crypto_bot.sql` contains:
  - `crypto` table (per-pair params)
  - Orders
- Consider:
  - Adding **indexes** for performance.
  - Adding an **audit log** table.

### 📲 **Telegram**
- **send_telegram.php** handles sending but:
  - Messages need **consistency** (toast & Telegram should match).
  - Tokens & chat IDs should be externalized.

---

## 🔧 6. Suggested Improvements

✅ **Better Architecture**
- Create **OOP structure** (classes for Binance, Telegram, DB).

✅ **Centralized Error Handling**
- One JS/PHP system to:
  - Show error toast once.
  - Log to DB.
  - Send Telegram alert.

✅ **Optimize Binance Polling**
- Current polling is too frequent; implement caching.

✅ **UI Enhancements**
- Fix modal bugs (especially Base Expense).
- Consider TailwindCSS for cleaner, modern styling.

✅ **Security**
- Move sensitive credentials (Binance API keys, Telegram tokens) to:
  - `.env` file or
  - Secure DB storage.

---

## 🏁 7. How to Set Up Emporion from ZIP

1️⃣ **Install XAMPP (Windows) or LAMP (Linux)**.  
2️⃣ **Import DB** from `/db/crypto_bot.sql` using phpMyAdmin.  
3️⃣ **Configure Binance API keys** in `/includes/config.php`.  
4️⃣ **Access** `index.php` to log in.  
5️⃣ **Go to** `trade.php` and start setting up:
   - Select a crypto pair.
   - Set parameters.
   - Paste target prices.
   - Start the bot.

---

## 📘 8. Additional Documentation

- `/guide/` folder has **9 Markdown chapters** (Introduction, Setup, Interface, Parameters, Table Usage, Notifications, Backup, Errors, FAQ).
- Update them as you **fix bugs** or **add features**.

---

## 📌 9. Developer To-Do List

- [ ] Fix **Base Expense modal** → DB sync.
- [ ] Verify and fix **Buy/Sell table values**.
- [ ] Refactor **toast & Telegram messages** for consistency.
- [ ] Secure Telegram credentials (remove from inline code).
- [ ] Consolidate PHP logic into **services** for maintainability.
- [ ] Optimize Binance polling strategy.

---

## 🔐 Telegram Credentials (Current)

> ⚠️ **NOTE:** These are currently hardcoded.  
They should be **moved to DB or config file** for security.

```php
$telegram_credentials = [
    'TAO/BTC' => ['chat_id' => '344969533','token' => 'XXXX'],
    'EIGEN/BTC' => ['chat_id' => '344969533','token' => 'XXXX'],
    ...
];
```

---

### ✅ Final Note
Emporion 0.60 is **functional** but needs **cleanup, optimization, and security hardening**.  
This guide, combined with the included `/guide/` chapters, provides the **foundation for any developer** to take over, **understand the codebase**, and **improve the bot**.

