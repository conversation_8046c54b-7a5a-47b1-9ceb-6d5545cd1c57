# 📕 Capitolo 6 – Notifiche e monitoraggio

---

## 🎯 Obiettivo del capitolo

In questo capitolo scoprirai:

- Come funzionano le **notifiche visive (toast)** e i **suoni**
- Come vengono inviati i **messaggi Telegram**
- Cosa succede quando il bot si ferma o si disconnette
- Quali eventi generano notifiche automatiche

---

## 🔔 1. Toast mitologici (notifiche visive)

Emporion utilizza una serie di notifiche visive in **stile pergamena**, che appaiono in basso a destra della pagina `trade.php`.

### ✅ Esempi di toast visualizzati:

| Evento                         | Messaggio Toast                                    |
|--------------------------------|-----------------------------------------------------|
| Ordine eseguito (buy)         | 🛒 Acquisto completato a livello 3 – 0.00001100 BTC |
| Ordine eseguito (sell)        | 💰 Vendita completata a livello 3 – 0.00001180 BTC |
| Bot avviato                   | 🟢 Emporion Bot avviato – Ordini in ascolto         |
| Bot fermato                   | 🔴 Bot fermato – Nessun ordine attivo               |
| Disconnessione API Binance    | ⚠️ Errore rete: Binance non raggiungibile          |
| Riconnessione Binance         | 🔄 Connessione ripristinata con Binance             |

> I toast sono visibili per alcuni secondi e si chiudono automaticamente.

---

## 🔉 2. Notifiche sonore

Ogni ordine eseguito attiva un suono di conferma (es. tipo “forziere” o “cassa”).

> ⚠️ Il suono si ripete solo una volta per ordine, e viene bloccato se il bot è fermato.

Puoi personalizzare il suono nel file `js/toast.js` o sostituendo il file audio incluso.

---

## 💬 3. Notifiche Telegram

Emporion può inviare notifiche automatiche al tuo account Telegram, per ogni evento importante:

### ✅ Messaggi supportati:

- 🟢 Avvio bot (per una specifica crypto)
- 🔴 Stop bot
- 🛒 Acquisto completato
- 💰 Vendita completata
- ⚠️ Errore Binance (rete, API errata, chiavi mancanti)
- 🔄 Riconnessione dopo disconnessione

---

## 📲 Come configurare il bot Telegram

### Fase 1 – Crea il bot

1. Vai su [@BotFather](https://t.me/BotFather)
2. Scrivi `/start`, poi `/newbot`
3. Dai un nome e un username univoco
4. Copia il **TOKEN** fornito (es. `*********:ABC...`)

---

### Fase 2 – Ottieni il tuo chat ID

1. Invia un messaggio qualsiasi al bot appena creato
2. Apri nel browser questo link:
   ```
   https://api.telegram.org/bot<YOUR_TOKEN>/getUpdates
   ```
3. Cerca nel JSON:
   ```
   "chat": { "id": *********, ... }
   ```
   → `*********` è il tuo **chat_id**

---

### Fase 3 – Inserisci token e chat ID

Puoi farlo in due modi:

#### 🔧 Opzione 1 – Interfaccia grafica

- Clicca su **Configure Telegram Bot**
- Inserisci `TOKEN` e `Chat ID`
- Clicca **Save**

I dati saranno salvati nel browser e usati automaticamente.

---

#### 🧩 Opzione 2 – Codice (`trade.php`)

Nel file `trade.php` puoi inserire direttamente:

```php
$telegram_credentials = [
  'EIGEN/BTC' => [
    'chat_id' => '*********',
    'token' => '*********:ABCDEF...'
  ]
];
```

> 🔁 Questa opzione è utile per coppie crypto fisse o test in locale

---

## ✏️ Modificare o rimuovere le credenziali Telegram

- Per **modificare** token/chat ID:
  - Apri la modale Telegram → inserisci nuovi valori → salva
- Per **rimuoverli**:
  - Cancella i valori nella modale e salva
  - Oppure rimuovili da `localStorage` tramite console JS:
    ```js
    localStorage.removeItem("telegram_token");
    localStorage.removeItem("telegram_chat_id");
    ```

---

## 🧠 Funzionamento automatico

Quando viene eseguito un ordine o il bot cambia stato:

1. Viene generato un toast visivo
2. Se la crypto ha token/chat ID, viene inviato un messaggio Telegram
3. Il tutto avviene **automaticamente** se i dati sono salvati correttamente

---

## ⚠️ Errori e disconnessioni

Se Binance non risponde, Emporion mostra:

- 🔔 Toast: `⚠️ Binance non raggiungibile`
- 📩 Telegram: `⚠️ Errore di rete su Binance`
- 🔄 Al ritorno della connessione: `🔄 Riconnessione completata`

La funzione `showToastOnce()` evita notifiche duplicate in caso di errori ripetuti.

---

## 🧪 Diagnostica Telegram (test)

Puoi testare il bot cliccando **Configure Telegram Bot** e poi **"Save"**  
→ Se configurato correttamente, riceverai un messaggio automatico da Emporion.

---

## 📌 Conclusione del Capitolo

Ora sai come funzionano tutte le notifiche di Emporion:

- visive (toast)
- sonore
- Telegram

Nel prossimo capitolo vedremo come **esportare i dati e salvare/ripristinare il database.**

➡️ Prossimo capitolo: `Capitolo 7 – Esportazione e backup`

