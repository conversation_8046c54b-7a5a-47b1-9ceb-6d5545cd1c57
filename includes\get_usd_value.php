<?php
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $data = json_decode(file_get_contents("php://input"), true);
    $symbol = strtoupper(trim($data["asset"] ?? ""));
    $amount = floatval($data["amount"] ?? 0);

    if (!$symbol || $amount <= 0) {
        echo json_encode(["status" => false, "message" => "Dati non validi"]);
        exit;
    }

    if (in_array($symbol, ["USDT", "BUSD", "USD"])) {
        echo json_encode(["status" => true, "usd_value" => $amount]);
        exit;
    }

    $pair = $symbol . "USDT";
    $url = "https://api.binance.com/api/v3/ticker/price?symbol=$pair";

    $response = @file_get_contents($url);

    if ($response === FALSE) {
        echo json_encode(["status" => false, "message" => "Errore richiesta Binance"]);
        exit;
    }

    $json = json_decode($response, true);
    if (!isset($json["price"])) {
        echo json_encode(["status" => false, "message" => "Prezzo non trovato"]);
        exit;
    }

    $price = floatval($json["price"]);
    $usdValue = $amount * $price;

    echo json_encode(["status" => true, "usd_value" => $usdValue]);
}
?>
