# 📙 Capitolo 3 – Interfaccia di controllo

---

## 🎯 Obiettivo del capitolo

In questo capitolo analizziamo **la pagina principale `trade.php`** di Emporion.  
Vedremo in dettaglio:

- la struttura dell’interfaccia utente
- la funzione di ogni pulsante
- i dati visibili
- le notifiche e lo stato operativo

---

## 🧭 Panoramica della schermata

La pagina si divide in **3 colonne principali**:

```
+-------------------+---------------------------+-------------------+
| COLONNA SINISTRA  |   COLONNA CENTRALE        |  COLONNA DESTRA   |
| - Resistance      | - Parametri bot           | - Azioni crypto   |
| - Support         | - Base Expense, ecc.      | - Telegram config |
| - Crypto Pair     | - Target Prices           | - Export/Backup   |
| - Actual Price    |                           | - Logout/User     |
+-------------------+---------------------------+-------------------+
```

Sotto il titolo appare una barra decorativa:

### 🟢 `Emporion is Open / Closed`  
Indica lo stato attuale del bot per la crypto selezionata.

### 📊 Riepilogo ordini (2 righe)

```
Orders: 4  ✦  Levels: 5  
Range: level46  ✦  TP: 0.00001131  ✦  Δ: +0.98%
```

---

## 🧱 Colonna sinistra – Info mercato

### 🔹 Resistance Value / Support Value
- **Valori massimo e minimo** dei target prices inseriti
- Calcolati automaticamente al salvataggio

### 🔹 Crypto Pair
- Menu a tendina con le crypto salvate
- La selezione imposta tutti i dati (target, parametri, bot Telegram)

### 🔹 Actual Price
- Prezzo attuale della crypto selezionata
- Si aggiorna ogni 2–3 secondi

---

## 🎛️ Colonna centrale – Parametri di configurazione

### ⚙️ Pulsanti principali:

- **Set Target Prices**  
  Apre una modale dove incollare o generare i livelli di prezzo

- **Set Base Expense**  
  Imposta il valore di partenza in `Crypto 2` per calcolare i livelli

- **Set Multiplicator**  
  Fattore moltiplicativo tra un livello e il successivo

- **Set Number of Digits**  
  Quanti decimali usare nel calcolo dei TP

- **Set Slippage %**  
  Percentuale massima di scostamento tollerato nei prezzi di esecuzione

---

### ⛏️ Visualizzazione parametri

Ogni valore impostato viene mostrato a fianco del relativo pulsante, es:

```
[Set Multiplicator]   ➤   0.20
```

---

### 🚀 Start/Stop Bot

- **Start Bot**  
  Avvia il polling dei prezzi e l’attivazione automatica degli ordini

- **Stop Bot**  
  Disattiva il monitoraggio e il trading per la crypto selezionata

> Quando il bot è attivo, **i campi di input vengono bloccati** e viene mostrato il messaggio **"Emporion is Open"**

---

## 📂 Colonna destra – Azioni operative

### 🔗 Add/Remove Crypto Pair
- Apre una modale per:
  - aggiungere nuove crypto
  - rimuovere coppie esistenti

### 💬 Configure Telegram Bot
- Apre una modale per:
  - inserire token e chat ID
  - testare le notifiche
  - salvare le credenziali (browser o database)

### 📤 Export Excel Table
- Esporta la tabella visibile in formato `.xlsx`
- Salva il file nella cartella `Excel/`

### 💾 Save SQL Database
- Salva una copia dell’intero DB `crypto_bot` nella cartella `db/`

### 📂 Load SQL Database
- Ti permette di caricare un file `.sql` e sovrascrivere l'intero database corrente

---

### 👥 Users Area + Log Out

- Pulsanti per accedere ad aree utente o uscire dal sistema

---

## 📑 Tabella ordini

Sotto l'interfaccia principale trovi la **tabella interattiva** degli ordini.

### Colonne principali:

| Colonna            | Significato                                          |
|--------------------|------------------------------------------------------|
| **ID**             | Es: `level46`, nome del livello                      |
| **Order State**    | `open`, `full`, o vuoto                              |
| **Order Level**    | Numero intero relativo al livello (es. 0, 1, 2...)   |
| **Target Price**   | Prezzo a cui piazzare l’ordine                       |
| **Price Deviation**| Scostamento rispetto al prezzo attuale              |
| **Actual Expense** | Importo calcolato per l’ordine                       |
| **Gains (50%)**    | Ricavo stimato alla vendita                          |

---

## 🔔 Notifiche e Stato

### ✅ Toast mitologici

Appaiono in basso a destra con messaggi come:

- “Ordine di acquisto completato a livello 3”
- “Errore: API Binance non raggiungibile”
- “Bot Emporion riattivato dopo disconnessione”

### 🔉 Audio
- Ogni ordine `FULL` genera un suono di conferma

### 💬 Telegram
- Se configurato, ogni evento importante viene anche notificato via bot Telegram

---

## 📌 Conclusione del Capitolo

Hai ora un quadro completo dell’interfaccia di Emporion.  
Nel prossimo capitolo vedremo **come impostare i parametri chiave (expense, multiplicator, slippage...)** per ogni crypto.

➡️ Prossimo capitolo: `Capitolo 4 – Parametri e strategia`

