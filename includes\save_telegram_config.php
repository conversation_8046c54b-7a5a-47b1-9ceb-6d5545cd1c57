<?php
$data = json_decode(file_get_contents("php://input"), true);
$token = $data["token"] ?? "";
$chat_id = $data["chat_id"] ?? "";

if (!$token || !$chat_id) {
  echo json_encode(["status" => false, "message" => "Token o Chat ID mancanti."]);
  exit;
}

// Salva in un file locale (puoi adattare per salvarlo nel database se preferisci)
$config = [
  "token" => $token,
  "chat_id" => $chat_id
];

file_put_contents("../telegram_config.json", json_encode($config));

echo json_encode(["status" => true]);
?>
