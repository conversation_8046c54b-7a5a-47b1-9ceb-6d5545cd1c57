# 📙 Capitolo 7 – Esportazione e backup

---

## 🎯 Obiettivo del capitolo

In questo capitolo imparerai:

- Come esportare la tabella ordini in Excel
- Come salvare e ripristinare l’intero database `crypto_bot`
- Dove si trovano i file generati
- Come usare comandi SQL per operazioni rapide

---

## 📤 1. Esporta tabella in Excel

### 📍 Posizione:
Pulsante: **Export Excel Table**  
Colonna destra in `trade.php`

### ✅ Cosa fa:

- Genera un file `.xlsx` con la tabella degli ordini attivi
- Il file contiene: ID, order level, stato, TP, percentuali, guadagni...
- Viene salvato automaticamente nella cartella:

```
/Excel/
```

### 📎 Nome file tipico:
```
EIGENBTC_export_2025-07-24.xlsx
```

---

## 💾 2. Salvataggio del database (SQL)

### 📍 Posizione:
Pulsante: **Save SQL Database**

### ✅ Cosa fa:

- Crea un backup completo del database `crypto_bot`
- Esporta tutte le tabelle MySQL (crypto, ordini, log...)
- Salva il file `.sql` nella cartella:

```
/db/
```

### 📎 Nome file tipico:
```
backup_20250724_153045.sql
```

---

## 📂 3. Caricamento di un database salvato

### 📍 Posizione:
Pulsante: **Load SQL Database**

### ✅ Cosa fa:

- Ti consente di selezionare un file `.sql` dal tuo PC
- Sostituisce l’intero contenuto del database attuale
- ⚠️ **Attenzione:** sovrascrive tutti i dati correnti

---

## 💻 4. Comandi SQL utili

Puoi eseguire i seguenti comandi direttamente in **phpMyAdmin** (nella sezione SQL), per gestire manualmente il DB.

### ✅ A. Eliminare tutto il contenuto del database `crypto_bot`:

```sql
DROP DATABASE IF EXISTS crypto_bot;
CREATE DATABASE crypto_bot;
```

### ✅ B. Esportare un backup manuale (da phpMyAdmin):

1. Seleziona `crypto_bot`
2. Clicca su **Esporta**
3. Scegli formato `SQL`
4. Salva il file `.sql` generato

### ✅ C. Importare un database `.sql`:

1. Clic su **Importa**
2. Seleziona il file `.sql` dal tuo PC
3. Clic su **Esegui**

---

## 🧠 Best practice per backup

| Situazione                  | Azione consigliata                            |
|-----------------------------|-----------------------------------------------|
| Prima di grandi modifiche   | Esegui **Save SQL Database**                  |
| Dopo un ciclo di ordini     | Esegui **Export Excel Table**                 |
| Uso prolungato multi-crypto | Fai backup settimanale della cartella `/db/`  |
| Versioning manuale          | Rinomina i file `.sql` con commenti (es. `v1.2`) |

---

## ⚠️ Avvisi

- Il pulsante "Load SQL Database" **non chiede conferma**: attenzione a non caricare backup errati
- I file `.sql` contengono tutte le crypto, ordini e configurazioni

---

## 📌 Conclusione del Capitolo

Ora sai come salvare e ripristinare tutti i tuoi dati con sicurezza.  
Nel prossimo capitolo vedremo **come risolvere errori comuni**, diagnosticare problemi e ottimizzare l’uso di Emporion.

➡️ Prossimo capitolo: `Capitolo 8 – Errori comuni e soluzioni`

