@import url('https://fonts.googleapis.com/css2?family=Cinzel&display=swap');

body {
  background: url('../img/Emporion.png') no-repeat center center fixed;
  background-size: 100% 100%; /* forza adattamento pieno */
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;

  font-family: "Cinzel", serif;
  color: #4b2e0e;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
}

.login-page {
  width: 90%;
  max-width: 400px;
  margin: 10vh auto 0 auto; /* top auto bottom center */
  padding: 20px;
  box-sizing: border-box;
}

.form {
  width: 100%;
  max-width: 400px;
  background-color: #fcf8f3;
   opacity: 0.9; /* opacità del contenuto intero */
  margin: 0 auto 5vh;
  padding: 30px 20px;
  text-align: center;
  border: 2px solid #bfa06a;
  border-radius: 15px;
  box-shadow: 0 0 15px rgba(139, 69, 19, 0.5);
  font-family: "Cinzel", serif;
  color: #4b2e0e;
}

.form input {
  font-family: "Cinzel", serif;
  outline: none;
  background: rgba(255, 248, 240, 0.9);
  width: 85%;           /* più strette */
  max-width: 340px;
  border: 1px solid #bfa06a;
  margin: 0 0 12px;
  padding: 8px;         /* meno alte */
  box-sizing: border-box;
  font-size: 12px;
  border-radius: 6px;
  color: #4b2e0e;
}


.form input:focus {
  background: rgba(255, 248, 240, 1); /* stesso colore delle textbox */
  border: 1px solid #a46a3f;          /* bordi bronzati */
  box-shadow: 0 0 5px rgba(165, 105, 60, 0.5); /* effetto luce dorata */
  outline: none;
  color: #4b2e0e;
}


.form .button {
  font-family: "Cinzel", serif;
  text-transform: uppercase;
  background: #a46a3f;
  width: 85%;           /* corrispondente allo stesso width */
  max-width: 340px;
  border: 0;
  padding: 8px;
  color: white;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  border-radius: 6px;
  transition: background 0.3s ease;
}


.form .button:hover {
  background: #8e5a32;
}

.form .message {
  margin: 15px 0 0;
  color: #7a4e2c;
  font-size: 12px;
}

.form .message a {
  color: #a46a3f;
  text-decoration: none;
}


.header-title {
  text-align: center;
  font-family: "Cinzel", serif;
  font-size: 2.2em;
  font-weight: bold;
  color: #4b2e0e;
  text-shadow: 2px 2px #e3caa5;
  margin-bottom: 40px;
  padding: 25px;
  border: 5px solid #bfa06a;
  border-radius: 25px;
  background: linear-gradient(to right, #fdf6e3, #f3d9b1);
  width: 90%;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 0 40px rgba(120, 68, 20, 0.6), inset 0 0 10px rgba(191, 160, 106, 0.8);
  letter-spacing: 1px;
}

.footer-note {
  position: fixed;
  bottom: 10px;
  right: 15px;
  font-family: "Cinzel", serif;
  font-size: 11px;
  color: #f3e6c1; /* colore chiaro elegante */
  background: none;
  border: none;
  padding: 0;
  box-shadow: none;
}





input:-webkit-autofill,
input:-webkit-autofill:focus,
input:-webkit-autofill:hover,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 1000px rgba(255, 248, 240, 0.9) inset !important;
  -webkit-text-fill-color: #4b2e0e !important;
  transition: background-color 5000s ease-in-out 0s;
  font-family: "Cinzel", serif;
}

@media (max-width: 500px) {
  .header-title {
    font-size: 1.4em;
    padding: 15px;
  }

  .form input,
  .form .button {
    font-size: 11px;
    padding: 6px;
  }

  .form {
    padding: 20px 15px;
  }
}

