<?php 
    include "conn.php";
    session_start();

    $jsonData = file_get_contents("php://input");
    $data = json_decode($jsonData);

    $level = $data->level;
    $target_price = $data->target_price;
    $crypto = $data->crypto;
    $s_bid = $data->s_bid;
    $user_id = $_SESSION['user_id'];

    // Calcolo del moltiplicatore basato sul livello
    $multiplicator = (int) $level;

    // Formula per il suggested_bid
    $suggested_bid = $s_bid + pow($s_bid * 0.0125, $multiplicator);

    // Verifica che non esista già
    $check_sql = "SELECT * FROM trade WHERE pricelevel = '$level' AND crypto = '$crypto' AND user = '$user_id'";
    $runCheckQuery = mysqli_query($conn, $check_sql) or die("Check query failed");

    if (mysqli_num_rows($runCheckQuery) === 0) {

        $sql = "INSERT INTO trade (pricelevel, state_status, suggested_bid, target_price, crypto, `user`) 
                VALUES ('$level', 'open', '$suggested_bid', '$target_price', '$crypto', '$user_id')";

        $runQuery = mysqli_query($conn, $sql) or die("Run query failed");

        if($runQuery){
            echo json_encode(["massage"=>"trade added","status"=> true]);
        } else {
            echo json_encode(["massage"=>"trade","status"=>false]);
        }
    }

    mysqli_close($conn);
?>
