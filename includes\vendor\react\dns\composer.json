{"name": "react/dns", "description": "Async DNS resolver for ReactPHP", "keywords": ["dns", "dns-resolver", "ReactPHP", "async"], "license": "MIT", "authors": [{"name": "<PERSON>", "homepage": "https://clue.engineering/", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://wyrihaximus.net/", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://sorgalla.com/", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://cboden.dev/", "email": "<EMAIL>"}], "require": {"php": ">=5.3.0", "react/cache": "^1.0 || ^0.6 || ^0.5", "react/event-loop": "^1.2", "react/promise": "^3.0 || ^2.7 || ^1.2.1"}, "require-dev": {"phpunit/phpunit": "^9.5 || ^4.8.35", "react/async": "^4 || ^3 || ^2", "react/promise-timer": "^1.9"}, "autoload": {"psr-4": {"React\\Dns\\": "src/"}}, "autoload-dev": {"psr-4": {"React\\Tests\\Dns\\": "tests/"}}}