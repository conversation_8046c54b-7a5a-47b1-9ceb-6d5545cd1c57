<?php
session_start();
include "conn.php";

$data = json_decode(file_get_contents("php://input"), true);
$crypto = $data["crypto"];
$slippage = $data["slippage_num"];

$sql = "UPDATE crypto SET slippage_num = ? WHERE crypto_name = ? AND user = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("dss", $slippage, $crypto, $_SESSION['user_id']);
$stmt->execute();

echo json_encode(["status" => true]);
?>
