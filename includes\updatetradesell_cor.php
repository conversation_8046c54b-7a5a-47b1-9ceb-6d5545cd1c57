<?php 
include "conn.php";
include "functions.php";
session_start();

header('Content-Type: application/json');

$function = new Functions();
$jsonData = file_get_contents("php://input");
$data = json_decode($jsonData);

$dateTime = Date("m-d-Y");

// 🔵 ORDINE DI VENDITA TRAMITE API
$order = $function->sellUSDT($data->crypto, $data->future_sell_usdt, $data->actualPrice);

if (!empty($order)) {

    // ✅ Valore USDT/BTC ricevuto
    $usdtValue = ($order['cummulativeQuoteQty'] == 0) 
        ? $data->actualPrice * $order['origQty'] 
        : $order['cummulativeQuoteQty'];

    // ✅ Quantità venduta in crypto base
    $qty_sold = (float)$order['origQty'];

    // ✅ Prezzo di vendita
    $sell_price = (float)$data->actualPrice;

    // ✅ BTC/BNB ricevuti
    $btc_received = (float)$usdtValue;

    // ✅ Prendo gli asset base/quote reali (es. ENA / BNB)
    $pair = strtoupper($data->crypto);
    $baseAsset = substr($pair, 0, -3);   // esempio: ENABNB → ENA
    $quoteAsset = substr($pair, -3);     // esempio: ENABNB → BNB

    // ✅ Recupera il prezzo in USD della quote (es. BNB/USDT)
    $quoteAssetPriceUSD = 0;
    $quoteSymbol = $quoteAsset . "USDT"; // es. BNB -> BNBUSDT

    try {
        $binancePriceApi = file_get_contents("https://api.binance.com/api/v3/ticker/price?symbol={$quoteSymbol}");
        if ($binancePriceApi) {
            $priceData = json_decode($binancePriceApi, true);
            $quoteAssetPriceUSD = isset($priceData['price']) ? (float)$priceData['price'] : 0;
        }
    } catch (Exception $e) {
        $quoteAssetPriceUSD = 0;
    }

    // ✅ CALCOLO DELL’ULTIMO POSSESSO (per il residuo)
    $get_lasted_possessed_crypto = "
        SELECT possessed_crypto FROM `history` 
        WHERE crypto = '$data->crypto' AND `user` = '{$_SESSION['user_id']}' 
        ORDER BY history_id LIMIT 1";
    $runQuery_possessed_crypto = mysqli_query($conn, $get_lasted_possessed_crypto);

    if (!$runQuery_possessed_crypto) {
        echo json_encode([
            "status" => false,
            "message" => "Errore SELECT possessed_crypto",
            "error" => mysqli_error($conn)
        ]);
        exit;
    }

    $result = mysqli_fetch_all($runQuery_possessed_crypto, MYSQLI_ASSOC);
    $previous_possessed = (float)$result[0]['possessed_crypto'];

    // ✅ Crypto residua dopo la vendita
    $crypto_residual = $previous_possessed - $qty_sold;

    // ✅ Valore residuo in USD (opzionale)
    $actual_val = $data->actualPrice * $crypto_residual;

    // ✅ Aggiorna tabella trade
    $s_bid = floatval($data->s_bid);
    $multiplicator = floatval($data->multiplicator);
    $pricelevel = intval($data->pricelevel);

    $suggested_bid = $s_bid * pow($multiplicator, $pricelevel);
    $suggested_bid = number_format($suggested_bid, 10, '.', '');

    $sql = "UPDATE `trade` SET 
                `state_status` = '$data->state_status',
                `suggested_bid` = '$suggested_bid',
                `bid` = 0,
                `crypto_var` = 0,
                `on_actual_price` = 0,
                `ricavo` = 0,
                `future_sell_usdt` = 0,
                `crypto_received` = 0 
            WHERE 
                `pricelevel` = '$data->pricelevel' AND 
                `crypto` = '$data->crypto' AND 
                `user` = '{$_SESSION['user_id']}'";

    $runQuery = mysqli_query($conn, $sql);
    if (!$runQuery) {
        echo json_encode([
            "status" => false,
            "message" => "Errore SQL (update trade SELL)",
            "error" => mysqli_error($conn)
        ]);
        exit;
    }

    // ✅ Registra in history
    $sql2 = "INSERT INTO `history`(
                `history_date`, `operation`, `usdt_value`, 
                `crypto_var`, `possessed_crypto`, `buy_sell_price`, 
                `actual_value`, `crypto`, `user`
            ) VALUES (
                '$dateTime', '$data->action', '{$usdtValue}', 
                '{$order['origQty']}', '$crypto_residual', 
                '$sell_price', '$actual_val', 
                '$data->crypto', '{$_SESSION['user_id']}'
            )";

    $run_query_sql2 = mysqli_query($conn, $sql2);
    if (!$run_query_sql2) {
        echo json_encode([
            "status" => false,
            "message" => "Errore INSERT history",
            "error" => mysqli_error($conn)
        ]);
        exit;
    }

    // 🔢 Calcolo campi toast usando il prezzo reale USD
    $qty_usd = $qty_sold * $sell_price * $quoteAssetPriceUSD;
    $ricavo_usd = $btc_received * $quoteAssetPriceUSD;
    $crypto_half_usd = $crypto_residual * $sell_price * $quoteAssetPriceUSD;

    // ✅ RISPOSTA COMPLETA PER TOAST E TELEGRAM
    echo json_encode([
        "status" => true,
        "trade_id" => $data->pricelevel,   // ✅ NON più “levellevel50”
        "target_price" => $sell_price,
        "qty" => $qty_sold,
        "qty_usd" => number_format($qty_usd, 2),
        "ricavo_usd" => number_format($ricavo_usd, 2),
        "usdt_half" => $btc_received,   
        "usdt_half_usd" => number_format($btc_received * $quoteAssetPriceUSD, 2),
        "crypto_half" => $crypto_residual,
        "crypto_half_usd" => number_format($crypto_half_usd, 2),
        "base" => $baseAsset,   // ✅ es. ENA
        "quote" => $quoteAsset  // ✅ es. BNB
    ]);

} else {
    echo json_encode([
        "status" => false,
        "massage" => "Problem with api",
        "data" => $order
    ]);
}

mysqli_close($conn);
?>
