<?php 
require 'vendor/autoload.php';

use Binance\API;

class Functions {

    private $api_key;
    private $api_secret;
    private $api;

    public function __construct() {
        $this->api_key = $_SESSION['api_key'];
        $this->api_secret = $_SESSION['api_secret'];
        $this->api = new Binance\API($this->api_key, $this->api_secret);
    }

    public function checkApi() {
        try {
            return $this->api->account();
        } catch (Exception $e) {
            echo "API Key or Secret Key is incorrect!\n";
            echo "Error: " . $e->getMessage() . "\n";
        }
    }

    public function checkCryptoAvailable($crypto) {
        try {
            return $this->api->price($crypto);
        } catch (Exception $e) {
            return false;
        }
    }

    public function buyUSDT($symbol, $amount, $price) {
        try {
            $exchangeInfo = $this->api->exchangeInfo();
            $filters = null;
            foreach ($exchangeInfo['symbols'] as $s) {
                if ($s['symbol'] === $symbol) {
                    $filters = $s['filters'];
                    break;
                }
            }

            if (!$filters) return false;

            $lotSizeFilter = array_values(array_filter($filters, function ($filter) {
                return $filter['filterType'] === 'LOT_SIZE';
            }));

            $stepSize = isset($lotSizeFilter[0]['stepSize']) ? floatval($lotSizeFilter[0]['stepSize']) : 1;

            // ✅ Quantità massima acquistabile senza superare il limite
            $max_quantity = floor(($amount / $price) / $stepSize) * $stepSize;
            $max_quantity = number_format($max_quantity, 8, '.', '');

            if (($max_quantity * $price) > $amount) {
                $max_quantity -= $stepSize;
                $max_quantity = number_format($max_quantity, 8, '.', '');
            }

            return $this->api->buy($symbol, $max_quantity, $price);
        } catch (Exception $e) {
            return false;
        }
    }

    public function sellUSDT($symbol, $amount, $price) {
        try {
            $exchangeInfo = $this->api->exchangeInfo();
            $filters = null;
            foreach ($exchangeInfo['symbols'] as $s) {
                if ($s['symbol'] === $symbol) {
                    $filters = $s['filters'];
                    break;
                }
            }

            if (!$filters) return false;

            $lotSizeFilter = array_values(array_filter($filters, function ($filter) {
                return $filter['filterType'] === 'LOT_SIZE';
            }));

            $stepSize = isset($lotSizeFilter[0]['stepSize']) ? floatval($lotSizeFilter[0]['stepSize']) : 1;

            // ✅ Quantità massima vendibile senza superare l’importo previsto
            $max_quantity = floor(($amount / $price) / $stepSize) * $stepSize;
            $max_quantity = number_format($max_quantity, 8, '.', '');

            if (($max_quantity * $price) > $amount) {
                $max_quantity -= $stepSize;
                $max_quantity = number_format($max_quantity, 8, '.', '');
            }

            return $this->api->sell($symbol, $max_quantity, $price);
        } catch (Exception $e) {
            return false;
        }
    }
}
?>
