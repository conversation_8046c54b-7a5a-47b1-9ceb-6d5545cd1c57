<?php
include "conn.php";
session_start();

$data = json_decode(file_get_contents("php://input"), true);
$crypto = $data['crypto'];
$s_bid = floatval($data['s_bid']);
$user_id = $_SESSION['user_id'];

$sql = "UPDATE crypto SET s_bid = ? WHERE crypto_name = ? AND user = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("dsi", $s_bid, $crypto, $user_id);

if ($stmt->execute()) {
    echo json_encode(["status" => true]);
} else {
    echo json_encode(["status" => false, "message" => $conn->error]);
}

$stmt->close();
?>
