# 📘 Capitolo 4 – Parametri e strategia

---

## 🎯 Obiettivo del capitolo

In questo capitolo imparerai a:

- Comprendere il significato di ogni parametro configurabile
- Sapere come influisce sul calcolo dei livelli
- Impostare i valori corretti per la tua strategia
- Usare i modali per salvare i parametri crypto-specifici

---

## 🔧 Dove si impostano i parametri?

Tutti i parametri sono visibili e modificabili nella **colonna centrale** della pagina `trade.php`, attraverso pulsanti e modali come:

```
[Set Base Expense]        ➤   --  
[Set Multiplicator]       ➤   --  
[Set Number of Digits]    ➤   --  
[Set Slippage %]          ➤   --
```

⚠️ Questi valori sono **indipendenti per ogni crypto** salvata e restano memorizzati nel database.

---

## 🧮 Parametri uno per uno

---

### 1️⃣ Base Expense (s_bid)

> **Definizione**: Il valore di spesa iniziale in *Crypto 2* per il livello 0

💡 **Esempio**:
Se stai tradando **EIGEN/BTC** e vuoi spendere 0.00016948 BTC al primo ordine:

```
Base Expense = 0.00016948
```

Ogni livello superiore o inferiore verrà calcolato a partire da questo valore, moltiplicato secondo `multiplicator`.

---

### 2️⃣ Multiplicator

> **Definizione**: Fattore di crescita (o decrescita) tra un livello e il successivo

💡 **Esempio**:

- Livello 0: 0.00016948
- Livello 1: 0.00016948 × (1 + 0.2) = 0.00020338
- Livello 2: 0.00016948 × (1 + 0.2)² = 0.00024405

📌 **Usi comuni**:
- 0.05 per crescita graduale (5%)
- 0.2 per strategie più aggressive

---

### 3️⃣ Number of Digits

> **Definizione**: Quanti decimali usare per il calcolo dei target price e expense

🎯 Esempio per `digits = 8`:
```
0.000169482138 → 0.00016948
```

🟡 Binance supporta:
- 6–8 decimali per coppie come BTCUSDT, ETHBTC
- 2–4 per coppie con token stabili o a valore intero

---

### 4️⃣ Slippage %

> **Definizione**: Margine percentuale accettabile tra prezzo target e prezzo attuale

🔐 Evita esecuzioni fuori range.

💡 **Esempio**:  
Target = 0.00001100  
Slippage = 2%  
→ Esegue solo se il prezzo reale è **entro** ±0.00000022

---

## 🧠 Strategie consigliate

### 🔸 Strategia conservativa (scalping passivo)
- Multiplicator: `0.05`
- Slippage: `0.5%`
- Digits: `8`
- Base Expense: ~piccolo (es. 0.00005 BTC)

### 🔸 Strategia media (step-ladder)
- Multiplicator: `0.15`
- Slippage: `1%`
- Digits: `8`
- Base Expense: intermedio

### 🔸 Strategia aggressiva (buy & hold a livelli)
- Multiplicator: `0.25` o più
- Slippage: `3–5%`
- Base Expense: elevata (più capitalizzata)

---

## 📝 Come modificare i parametri

1. Premi il pulsante `Set ...` corrispondente  
2. Nella modale, inserisci il valore  
3. Premi **Save**  
4. Il valore viene immediatamente salvato per quella crypto nel database

---

## 💡 Note tecniche

- I valori sono salvati nella tabella **`crypto`** del database `crypto_bot`
- Ogni record ha i campi:
  ```
  crypto_name, user, s_bid, multiplicator, digit_num, slippage_num
  ```

---

## 🔁 Modifica rapida multipla

Puoi impostare tutti i parametri prima di cliccare su **“Set Target Prices”**  
⚠️ Se il bot è attivo, i campi vengono **bloccati** per sicurezza.

---

## 📌 Conclusione del Capitolo

Ora sai come controllare ogni parametro strategico e adattarlo alla crypto selezionata.  
Nel prossimo capitolo vedremo **come funziona la tabella degli ordini**, come leggerla e come interagire con essa.

➡️ Prossimo capitolo: `Capitolo 5 – Uso della tabella`

