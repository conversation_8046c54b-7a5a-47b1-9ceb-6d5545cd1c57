<?php
// ✅ Riceve: token, chat_id, message
$data = json_decode(file_get_contents("php://input"), true);

$token = $data['token'] ?? null;
$chat_id = $data['chat_id'] ?? null;
$message = $data['message'] ?? null;

if (!$token || !$chat_id || !$message) {
    echo json_encode(["status" => false, "message" => "Missing parameters"]);
    exit;
}

$url = "https://api.telegram.org/bot$token/sendMessage";
$params = [
    'chat_id' => $chat_id,
    'text' => $message,
    'parse_mode' => 'HTML'
];

$options = [
    'http' => [
        'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
        'method'  => 'POST',
        'content' => http_build_query($params),
        'timeout' => 5
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo json_encode(["status" => false, "message" => "Failed to contact Telegram"]);
} else {
    echo json_encode(["status" => true]);
}
?>
