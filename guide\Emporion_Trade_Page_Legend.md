# Emporion Bot – Trade Page Legend & Functional Guide

This document serves as a **legend/manual** for the `trade.php` page of Emporion Bot.  
It explains **every element** visible to the user: buttons, modals, input fields, table behavior, toast notifications, Telegram messages, and also the related **History Log** and **Users** sections.

---

## 🏛 1. Trade Page Overview

When you log in, `trade.php` is the **main dashboard** where you manage all trading activity.  
The page is divided into **sections**:

1️⃣ **Top Bar**  
2️⃣ **Left Column – Bot Controls**  
3️⃣ **Center Column – Parameters & Trading Table**  
4️⃣ **Right Column – Utilities & Account**  
5️⃣ **Toast Notification Area** (hidden by default)  

---

## 🔝 2. Top Bar Elements

### 📜 **Emporion Title**
- Displays: `Emporion ✦ [Selected Crypto Pair]`
- Updates dynamically based on the crypto pair chosen from dropdown.

### 🔽 **Crypto Pair Dropdown**
- Lets you select which **pair** (e.g. `TAO/BTC`, `ETH/USDT`) the bot will manage.
- When a pair is selected:
  - Parameters (s_bid, slippage, etc.) are loaded from the DB.
  - Telegram token & chat_id are retrieved.

### 💬 **Emporion Status (Open/Closed)**
- Shows if the bot is currently running for the selected pair:
  - 🟢 **Open** → Bot active.
  - 🔴 **Closed** → Bot stopped.

### 📡 **Public IP Display**
- Shows the server’s current public IP (useful for remote monitoring).

### 📊 **Ticker Strip (if enabled)**
- Shows live prices & 24h variation for saved crypto pairs.
- Updates every 120 seconds.

---

## ⬅️ 3. Left Column – Bot Control Panel

Contains **essential bot controls**:

- **Start Bot / Stop Bot**  
  - Starts or halts the trading bot for the selected pair.
  - **Start Bot** also sends a **Telegram message**:  
    > 🟢 *Emporion Bot started – now active.*
  - **Stop Bot** sends:  
    > 🔴 *Emporion Bot stopped.*

- **Set Target Prices (Modal)**  
  - Opens a **textarea modal**. User pastes target prices (from Excel or text).  
  - On save → converts and stores them in DB, populates the trading table.

- **Set Base Expense (Modal)**  
  - Sets **base cost** for orders of selected pair.  
  - Stored in DB (linked to that crypto).  
  - **Issue:** currently this modal may not update DB (bug to fix).

- **Multiplicator / Digits / Slippage Buttons**  
  - Each opens a modal to set respective values:
    - **Multiplicator** → Affects suggested expense calculation.
    - **Digits** → Controls decimal precision for prices.
    - **Slippage** → Defines acceptable deviation from target.

---

## 🎛 4. Center Column – Trading Parameters & Table

### 🛠 **Parameter Display**
- Shows the current values for:
  - **Base Expense**
  - **Multiplicator**
  - **Digits**
  - **Slippage**  
Each is **editable only via modal**.

### 📋 **Trading Table**
This is the **heart of the bot**, displaying all Target Prices.

Columns typically include:
- **Level** → Target level (1, 2, 3…)
- **Target Price** → The price at which order triggers.
- **Suggested Expense** → Calculated based on s_bid + multiplicator.
- **Expense** → The real expense used for the order.
- **State** → Shows order status:
  - `WAITING` – Waiting for price trigger.
  - `FULL` – Order executed.
  - `PARTIAL` – Order partially filled.
- **Variation** → Shows price change vs. target.

**Table Behavior:**
- When price hits target:
  - State changes → `FULL`.
  - Expense updates with real order values.
- Uses `makeTable()` in `action.js` to **rebuild dynamically** after every order.

---

## ➡️ 5. Right Column – Utilities & Account

- **Add/Remove Crypto Pair (Modal)**  
  - Add new pair or remove existing one.

- **Configure Telegram Bot (Modal)**  
  - Set or modify `token` and `chat_id` for selected pair.

- **Export Excel Table**  
  - Downloads trade table as Excel file in `/Excel` folder.
  - Converts US format to Italian Excel format.

- **Save SQL Database / Load SQL Database**  
  - Backup or restore `crypto_bot` DB.

- **Users Area / Log Out**  
  - Manage users or exit the bot.

---

## 📑 6. Modals Explanation

### 📥 **Set Target Prices Modal**
- **Textarea** for pasting target prices.  
- **Save** → processes data, updates DB, regenerates trade table.

### 💰 **Set Base Expense Modal**
- Input field for base expense value.  
- Updates DB & UI (bug: DB update issue in v0.60).

### 🔢 **Multiplicator / Digits / Slippage Modals**
- Similar structure:
  - Number input.
  - Save button updates DB (specific to crypto pair).

### 📲 **Configure Telegram Bot Modal**
- Inputs for:
  - Telegram Bot Token
  - Telegram Chat ID
- Should save to DB (currently partially hardcoded).

### ➕ **Add/Remove Crypto Modal**
- Add a new trading pair (saves to DB).
- Remove pair (deletes from DB).

---

## 🔔 7. Toast Notifications

Toast messages are **visual pop-ups** shown bottom-right.

### 🔵 **Types of Toasts:**
- ✅ **Order Filled** → Displays:
  - Target Price
  - Level
  - Amount
  - Expense in USD
  - Net Revenue in USD
- ❌ **Error Toasts** → API issues, disconnection, etc.
- 🔄 **Reconnection Toast** → Shows once when Binance reconnects.

### 🛠 **showToastOnce()**
- Prevents duplicate spam of same toast.

### 📂 **toast.js**
- Contains toast rendering logic & templates.

---

## 📲 8. Telegram Notifications

Every major event sends a **Telegram message** (per crypto pair):
- 🟢 Bot Started  
- 🔴 Bot Stopped  
- ✅ Buy/Sell executed (details included)  
- 🔌 Network error / disconnection  
- 🔁 Reconnection notice

### 🔗 **sendToTelegram()**
- Called from `action.js` after events or from PHP after DB updates.

**Important:** Telegram credentials currently **hardcoded** in some files. Needs cleanup.

---

## 📜 9. History Log Section

- Accessible via **History.php**.
- Shows **executed orders**:
  - Date/Time
  - Pair
  - Type (Buy/Sell)
  - Price
  - Amount
- Useful for **auditing and debugging**.

**Backend:** `history_cor.php` handles data fetching.  
**Frontend:** `history.js` dynamically updates table.

---

## 👥 10. Users Section

- Accessible via **users.php**.
- Allows:
  - Viewing existing users.
  - Adding users (`add-user.php`).
  - Removing users.

Currently **basic authentication** system; consider adding roles/permissions.

---

## ✅ 11. Summary for Developers

### 🔍 **Where to Look**
- **trade.php** → Main structure.
- **action.js** → Price polling, table refresh, toast, Telegram calls.
- **toast.js** → Toast templates.
- **send_telegram.php** → Telegram backend.

### 🔧 **What Needs Attention**
- Fix **Base Expense modal** (DB update issue).
- Correct **table data mismatch** after buy/sell.
- Centralize **Telegram tokens** (remove from hardcode).
- Improve **toast-telegram consistency** (same data & format).

### 🏗 **Recommended Improvements**
- Convert `includes/` scripts to **OOP services**.
- Add **logging & debugging hooks** for each trade action.
- Secure sensitive data (API keys, tokens).

---
